# 统一门户系统项目规范与开发指南

## 项目概述

统一门户系统(UnifiedPortal)是一个基于Go语言开发的云服务管理平台后端系统。该项目采用模块化设计，集成了Kubernetes生态系统与微服务架构，提供统一的云服务管理接口。

## 代码组织

### 标准目录结构

```
backend/
  - cmd/                    # 应用入口点
    - backend-server/       # 主服务入口
      - app/                # 应用初始化与配置
      - main.go             # 程序入口
  - pkg/                    # 核心包目录
    - router/               # HTTP路由定义
    - handler/              # 请求处理层
    - models/               # 数据模型定义
      - dao/                # 数据库模型
    - utils/                # 通用工具函数
    - constants/            # 常量定义
    - errors/               # 错误处理
    - logger/               # 日志管理
    - controllers/          # K8s控制器
    - task/                 # 后台任务
    - resources/            # 数据库筛选
    - initialize/           # 应用初始化
    - feign/                # 服务间调用
    - database/             # 数据库连接
    - config/               # 配置管理
```

### 命名规范

- **文件命名**: 小写字母，单词间用下划线分隔（如 `node_pool.go`）
- **目录命名**: 小写字母，单词间不用分隔符（如 `nodepool`）
- **接口命名**: 大写字母开头，采用驼峰式（如 `Handler`）
- **结构体命名**: 大写字母开头，采用驼峰式（如 `NodePoolController`）
- **方法命名**: 大写字母开头，采用驼峰式（如 `CreateNodePool`）
- **变量命名**: 小写字母开头，采用驼峰式（如 `nodePoolList`）

## 模块规范

### 1. 路由层 (pkg/router/)

**职责**:
- 定义和注册HTTP路由
- 解析请求参数
- 进行基本参数校验
- 调用Handler层处理业务逻辑
- 构建HTTP响应

**规范**:
- 控制器必须实现`common.ApiController`接口
- 控制器命名格式为`{模块名}Controller`
- 必须使用`utils.Success`和`utils.Failed`处理响应
- 路由API前缀应使用`/apis/v1`，使用`utils.ApiV1Group`替换
- 所有公开API必须有Swagger注解文档
- 必须使用`errors.NewFromError`处理错误
- 涉及分页的接口必须使用`utils.ParseQueryParams`解析查询参数，并使用`models.PageableResponse`返回结果
- 涉及List的接口，支持sort_order, sort_func, sort_name, selector,page_num, page_size参数, 参数必须使用query参数， 使用`resources.Filter`过滤器过滤结果
- 支持如下查询参数:
    - sort_order: 排序方式 asc 或 desc，默认 desc
    - sort_func: 排序字段比较方式，例如 time, string, number
    - sort_name: 排序字段，通过类似 JSONPath 的方式获取, 默认为 createTime
    - selector: 符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询 selector=name~a,namespace=default
    - page_num: 页码, page_num和page_size必须同时存在，否则不进行分页
    - page_size: 每页大小, page_num和page_size必须同时存在，否则不进行分页

**示例**:

```go
// pkg/router/nodepool/controller.go

import (
    "github.com/gin-gonic/gin"
    "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
    "harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
)

func (c *NodePoolController) RegisterRouter(root *gin.RouterGroup) {
    group := root.Group(utils.ApiV1Group + "/clusters/:clusterName/nodepools")
    {
        group.POST("", c.createNodePool)
        group.GET(":nodePoolName", c.getNodePool)
        group.GET("", c.listNodePool)
    }
}

// @Summary 创建节点池
// @Description 在指定集群中创建新的节点池
// @Tags NodePool
// @Accept json
// @Produce json
// @Param clusterName path string true "集群名称"
// @Param request body nodepool.CreateNodePoolRequest true "创建节点池请求"
// @Success 201 {object} utils.Response
// @Failure 400 {object} utils.Response
// @Router /apis/v1/clusters/{clusterName}/nodepools [post]
func (c *NodePoolController) createNodePool(ctx *gin.Context) {
    clusterName := ctx.Param("clusterName")
    req := &nodepool.CreateNodePoolRequest{}
    if err := ctx.ShouldBindJSON(req); err != nil {
        utils.Failed(ctx, err)
        return
    }

    resp, err := c.nodePoolHandler.CreateNodePool(ctx, clusterName, req)
    if err != nil {
        utils.Failed(ctx, errors.NewFromError(ctx, err))
        return
    }

    utils.Success(ctx, resp)
}


// @Summary 获取节点池
// @Description 在指定集群中获取节点池
// @Tags NodePool
// @Accept json
// @Produce json
// @Param clusterName path string true "集群名称"
// @Param nodePoolName path string true "节点池名称"
// @Success 201 {object} utils.Response
// @Failure 400 {object} utils.Response
// @Router /apis/v1/clusters/{clusterName}/nodepools/{nodePoolName} [get]
func (c *NodePoolController) getNodePool(ctx *gin.Context) {
    clusterName := ctx.Param("clusterName")
    nodePoolName := ctx.Param("nodePoolName")
    req := &nodepool.GetNodePoolRequest{
        ClusterName:  clusterName,
        NodePoolName: nodePoolName,
    }
    resp, err := c.nodePoolHandler.GetNodePool(ctx, req)
    if err != nil {
        utils.Failed(ctx, errors.NewFromError(ctx, err))
        return
    }

    utils.Success(ctx, resp)
}

// @Summary 获取节点池列表
// @Description 在指定集群中获取节点池
// @Tags NodePool
// @Accept json
// @Produce json
// @Param clusterName path string true "集群名称"
// @Param page_num query int false "页码, 默认为1"
// @Param page_size query int false "每页大小, 默认为10"
// @Param sort_order query string false "排序方式 asc 或 desc，默认 desc"
// @Param sort_func query string false "排序字段比较方式，例如 time, string, number"
// @Param sort_name query string false "排序字段，通过类似 JSONPath 的方式获取, 默认为 createTime"
// @Param selector query string false "符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询 selector=name~a,namespace=default"
// @Success 201 {object} utils.Response
// @Failure 400 {object} utils.Response
// @Router /apis/v1/clusters/{clusterName}/nodepools/{nodePoolName} [get]
func (c *NodePoolController) listNodePool(ctx *gin.Context) {
    clusterName := ctx.Param("clusterName")
    req := &nodepool.ListNodePoolRequest{
        ClusterName:  clusterName,
    }
    // 解析查询参数
    req.Filter = utils.ParseQueryParams[*nodepool.NodePool](ctx)
    resp, err := c.nodePoolHandler.ListNodePool(ctx, req)
    if err != nil {
        utils.Failed(ctx, errors.NewFromError(ctx, err))
        return
    }

    utils.Success(ctx, resp)
}

```

### 2. 处理层 (pkg/handler/)

**职责**:
- 实现业务逻辑
- 与数据库交互
- 调用其他微服务
- 处理错误并返回适当结果

**规范**:
- Handler接口定义在`pkg/handler/{module}/interface.go`中
- 接口方法必须使用`context.Context`而非`gin.Context`
- 方法参数必须显式声明，不依赖HTTP框架
- 方法返回值应是具体业务对象或错误，不处理HTTP响应
- 每个模块必须创建独立子目录
- 错误处理应该使用`errors.Var.XX`的错误码进行处理，XX为错误码常量，如`errors.Var.NodePoolNotFound`
- 必须使用`logger.GetLogger()`获取日志实例
- 接口函数签名必须使用`(ctx context.Context, req *{module_model}.FuncName{Request}) (resp *{module_model}.FuncName{Response},err error)`
- 具体的`handler`,必须新建一个日志实例，如`logger.GetLogger().Named("nodepool")`记录日志

**示例**:

```go
// pkg/handler/nodepool/interface.go
package nodepool

import (
    "context"
    "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/nodepool"
)

type Handler interface {
    CreateNodePool(ctx context.Context, req *nodepool.CreateNodePoolRequest) (resp *nodepool.CreateNodePoolResponse,err error)
    GetNodePool(ctx context.Context, req *nodepool.GetNodePoolRequest) (resp *nodepool.GetNodePoolResponse, err error)
    ListNodePool(ctx context.Context, req *nodepool.ListNodePoolRequest) (resp *nodepool.ListNodePoolResponse, err error)
    // ... 其他方法
}

// pkg/handler/nodepool/nodepool.go
package nodepool

import (
    "context"
    "harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
    "harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
    "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
    "go.uber.org/zap"
)

type handler struct {
    logger *zap.Logger
}

func NewNodePoolHandler() Handler {
    return &handler{logger: logger.GetLogger().Named("nodepool")}
}

func (h *handler) CreateNodePool(ctx context.Context, req *nodepool.CreateNodePoolRequest)  (resp *nodepool.CreateNodePoolResponse,err error) {
    h.logger.Info("Creating node pool",
        zap.String("cluster", req.clusterName),
        zap.String("nodepoolName", req.NodePoolName),
    )
    
    // 业务逻辑实现
    // 如果报错 
    // return nil, errors.NewFromCodeWithMessage(errors.Var.CreateNodePoolError, "create node pool failed")
    
    return nil, nil
}

func (h *handler) GetNodePool(ctx context.Context, req *nodepool.GetNodePoolRequest)  (resp *nodepool.GetNodePoolResponse,err error) {
    h.logger.Info("Get node pool",
        zap.String("cluster", req.clusterName),
        zap.String("nodepoolName", req.NodePoolName),
    )
    
    // 业务逻辑实现
    // 如果报错 
    // return nil, errors.NewFromCodeWithMessage(errors.Var.GetNodePoolError, "get node pool failed")
    
    return nil, nil
}

func (h *handler) ListNodePool(ctx context.Context, req *nodepool.ListNodePoolRequest)  (resp *nodepool.ListNodePoolResponse,err error) {
    h.logger.Info("list node pool",
        zap.String("cluster", req.clusterName),
        zap.String("nodepoolName", req.NodePoolName),
    )
    // 获取集群客户端
    cluster, err := client.GetCluster(req.ClusterName)
    if err != nil {
        return nil, err
    }
    k8sClient := cluster.GetClient()
    // 使用k8sClient进行集群操作
    // 获取节点池列表
    nodepools := &nodepoolv1.NodePoolList{}
    if err := k8sClient.GetCtrlClient().List(ctx, nodepools); err != nil {
        return nil, err
    }
    // 将nodepools转换为ListNodePoolResponse
    var items []*nodepool.NodePool
    for _, nodepool := range nodepools.Items {
        items = append(items, &nodepool.NodePool{
            NodePoolName: nodepool.Name,
            ClusterName:  nodepool.ClusterName,
            Replicas:     nodepool.Replicas,
            Status:       nodepool.Status,
            CreationTime: nodepool.CreationTime,
        })
    }
    // 使用Filter过滤器过滤节点池列表
    resp, err := req.Filter.Filter(items)
    if err != nil {
        return nil, err
    }
    // 将resp转换为ListNodePoolResponse
    resp = &nodepool.ListNodePoolResponse{
        Items: resp.Items,
        Total: resp.Total,
    }
    // 业务逻辑实现
    // 如果报错 
    // return nil, errors.NewFromCodeWithMessage(errors.Var.ListNodePoolResponse, "list node pool failed")
    return resp, nil
}


```

### 3. 模型层 (pkg/models/)

**职责**:
- 定义请求和响应数据结构
- 定义数据库模型(DAO)
- 提供数据验证和转换

**规范**:
- API请求/响应模型必须定义在`pkg/models/{module}/`中
- 数据库模型必须定义在`pkg/models/dao/{db}/`中
- 必须使用结构体标签进行验证和序列化配置
- 数据库模型必须实现TableName()方法
- 涉及到List方法需要使用`resources.Filter`过滤器过滤结果
- 涉及到List方法需要使用`models.PageableResponse`返回结果

**示例**:

```go
// pkg/models/nodepool/nodepool.go
package nodepool

import (
    models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
    "harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
)

type CreateNodePoolRequest struct {
    ClusterName  string            `json:"clusterName"`
    NodePoolName string            `json:"nodePoolName" binding:"required"`
    Replicas     int32             `json:"replicas" binding:"required,min=0"`
    Flavor       string            `json:"flavor" binding:"required"`
    Labels       map[string]string `json:"labels,omitempty"`
}

type CreateNodePoolResponse struct {
    NodePoolName string `json:"nodePoolName"`
}

type GetNodePoolRequest struct {
    ClusterName  string            `json:"clusterName"`
    NodePoolName string            `json:"nodePoolName" binding:"required"`
}

type GetNodePoolResponse NodePool

type NodePool struct {
    NodePoolName string `json:"nodePoolName"`
    ClusterName  string `json:"clusterName"`
    Replicas     int32  `json:"replicas"`
    Status       string `json:"status"`
    CreationTime string `json:"creationTime"`
}

type ListNodePoolRequest struct {

    ClusterName  string `json:"clusterName"`
    // Filter 过滤器
    Filter resources.Filter[*NodePool] `json:"-"`
}

type ListNodePoolResponse *models.PageableResponse[*NodePool]

// pkg/models/dao/caas/nodepool.go
package caas

import "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao"

type NodePool struct {
    dao.CommonModel
    NodePoolName string `gorm:"column:node_pool_name;type:varchar(255);not null;uniqueIndex:uk_cluster_nodepool"`
    ClusterName  string `gorm:"column:cluster_name;type:varchar(255);not null;uniqueIndex:uk_cluster_nodepool"`
    Replicas     int32  `gorm:"column:replicas"`
    Status       string `gorm:"column:status;type:varchar(64)"`
}

func (NodePool) TableName() string {
    return "tb_nodepool"
}
```

### 4. 工具层 (pkg/utils/)

**职责**:
- 提供通用工具函数
- 实现跨模块复用功能

**规范**:
- 工具函数必须按功能分类到不同文件
- 针对特定业务模块的工具函数必须放在对应子目录
- 工具函数必须纯粹且无状态


### 5. 常量层 (pkg/constants/)

**职责**:
- 定义系统常量
- 提供状态、类型等枚举值

**规范**:
- 常量必须按业务模块分组
- 必须使用有意义的名称
- 禁止在代码中硬编码字符串和数字

**示例**:

```go
// pkg/constants/nodepool.go
package constants

const (
    // 状态常量
    NodePoolStatusCreating = "Creating"
    NodePoolStatusActive   = "Active"
    NodePoolStatusFailed   = "Failed"
    
    // 资源类型常量
    NodePoolTypeMaster  = "Master"
    NodePoolTypeWorker  = "Worker"
)
```

### 6. 错误处理 (pkg/errors/)

**职责**:
- 定义和管理错误
- 提供错误码和信息

**规范**:
- 必须使用`ErrorCode`结构定义错误
- 错误码范围必须按模块划分
- 错误信息必须支持国际化
- 错误码code全局唯一
- 必须使用`errors.NewFromCodeFormatMessage`创建错误

**示例**:

```go
// pkg/errors/constant.go
var (
    // 5001000-5001999为节点池模块的错误码
    NodePoolError = ErrorCode{ResponseCode: 5001000, Message: "Node pool operation failed"}
    NodePoolNotFound = ErrorCode{ResponseCode: 5001001, Message: "Node pool %s not found in cluster %s"}
)

// 错误使用示例
return errors.NewFromCodeFormatMessage(errors.Var.NodePoolNotFound, nodePoolName, clusterName)
```

### 7. 日志管理 (pkg/logger/)

**职责**:
- 提供统一日志记录
- 支持不同日志级别

**规范**:
- 必须使用`logger.GetLogger()`获取日志实例
- 关键操作必须记录INFO级别日志
- 错误必须记录ERROR级别日志
- 调试信息必须记录DEBUG级别日志
- 日志必须包含足够上下文信息

**示例**:

```go
logger.GetLogger().Info("Creating node pool",
    zap.String("cluster", clusterName),
    zap.String("nodePoolName", req.NodePoolName),
)

if err != nil {
    logger.GetLogger().Error("Failed to create node pool",
        zap.String("cluster", clusterName),
        zap.String("nodePoolName", req.NodePoolName),
        zap.Error(err),
    )
}
```

### 8. 数据库操作 (pkg/database/)

**职责**:
- 管理数据库连接和操作
- 提供事务支持

**规范**:
- 必须使用全局`database.CaasDB`或`database.AmpDB`实例
- 所有查询必须传递上下文
- 复杂操作必须使用事务处理
- 错误必须统一转换为应用错误码

**示例**:

```go
// 数据库操作示例
func (h *handler) GetNodePoolModel(ctx context.Context, id string) (*model.NodePool, error) {
    dao := &dao.NodePool{}
    if err := database.CaasDB.WithContext(ctx).Where("id = ?", id).First(dao).Error; err != nil {
        if err == gorm.ErrRecordNotFound {
            return nil, errors.NewFromCodeFormatMessage(errors.Var.ResourceNotFound, "Resource %s not found", id)
        }
        return nil, errors.NewFromCodeFormatMessage(errors.Var.DBError, "Database error: %v", err)
    }
    
    return convertDAOToModel(dao), nil
}
```

### 9. 微服务调用 (pkg/feign/)

**职责**:
- 处理与其他服务的HTTP通信
- 管理服务发现和负载均衡

**规范**:
- 必须使用`feign.NewHttpClient`或`feign.NewHttpClientWithBaseURL`创建客户端
- 每个外部服务必须定义清晰接口
- 必须处理和记录所有错误
- 必须使用上下文控制请求超时

**示例**:

```go
// pkg/feign/app-management/user.go
type UserClient interface {
    GetUserList(ctx context.Context) ([]byte, error)
}

type userClient struct {
    httpClient feign.HttpClientInterface
}

func NewUserClient(baseURL string) UserClient {
    return &userClient{
        httpClient: feign.NewHttpClientWithBaseURL(baseURL),
    }
}

func (c *userClient) GetUserList(ctx context.Context) ([]byte, error) {
    return c.httpClient.Get("/api/users")
}
```

### 10. 任务调度 (pkg/task/)

**职责**:
- 管理后台任务
- 处理定时作业

**规范**:
- 必须实现`Executor`接口
- 任务必须自包含且可重入
- 必须在`task.go`中注册定时任务
- 任务执行必须处理和记录所有错误

**示例**:

```go
// 任务实现示例
type myTask struct {}

func NewMyTask() Executor {
    return &myTask{}
}

func (t *myTask) execute() {
    logger.GetLogger().Info("Starting scheduled task")
    // 任务逻辑
}

// 在task.go中注册
func initCronTaskList() []cronTask {
    return []cronTask{
        {"0 */1 * * *", NewMyTask()}, // 每小时执行一次
    }
}
```

## 开发流程

### 新功能模块开发步骤

1. **定义模型** (pkg/models/{module}/)
   - 创建API请求/响应结构
   - 创建数据库模型

2. **实现处理器** (pkg/handler/{module}/)
   - 定义Handler接口
   - 实现接口方法

3. **定义路由** (pkg/router/{module}/)
   - 创建控制器并注册路由
   - 添加Swagger文档注解

4. **添加常量** (pkg/constants/{module}.go)
   - 定义状态、类型等常量

5. **定义错误码** (pkg/errors/constant.go)
   - 添加模块特定错误码

6. **编写工具函数** (pkg/utils/{module}/)
   - 实现模块特定辅助函数

7. **实现外部服务调用** (pkg/feign/yourmodule/)
   - 如需与其他服务交互

8. **注册路由** (pkg/router/router.go)
   - 将新模块的路由添加到全局路由

### 错误码分配规范

- **XX1000-XX1999**: 模块A错误
- **XX2000-XX2999**: 模块B错误
- **XX3000-XX3999**: 模块C错误
- XX 表示模块编号，如XX1000-XX1999表示模块A的错误码范围
- 每个模块必须在pkg/errors/constant.go中定义自己的错误码范围，错误码code全局唯一

### API响应格式

所有API响应必须遵循以下统一格式:

```json
{
  "code": 0,       // 0表示成功，非0表示错误码
  "message": "",   // 错误消息
  "data": {}       // 响应数据
}
```

## 代码质量标准

1. **可测试性**
   - 代码必须易于单元测试
   - 接口必须明确定义，便于Mock

2. **可维护性**
   - 必须遵循单一职责原则
   - 代码必须简洁，避免重复
   - 方法长度不得超过100行

3. **性能要求**
   - 避免不必要的数据库查询
   - 大批量操作必须分批处理
   - 必须考虑并发安全性

4. **安全要求**
   - 必须进行输入验证
   - 必须进行权限检查
   - 必须防止SQL注入和其他安全漏洞

## Kubernetes访问规范

Kubernetes集群访问必须使用`harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client.GetCluster(clusterId)`方法获取客户端, 并使用获取到的Cluster接口的`GetClient()`方法获取Kubernetes客户端

**示例**:

```go
import (
    "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
    corev1 "k8s.io/api/core/v1"
)

// ListPods 获取某一个集群的pod列表
func ListPods(ctx context.Context, clusterID string) (*corev1.PodList,error){
  // 访问Kubernetes集群标准方法
  cluster, err := client.GetCluster(clusterId)
  if err != nil {
      return nil, err
  }
  k8sClient := cluster.GetClient()
  // 使用k8sClient进行集群操作
  pods := &corev1.PodList{}
  if err := k8sClient.GetCtrlClient().List(ctx, pods); err != nil{
     return nil, err
  }
  return pods, nil
}


```

## 日志记录规范

1. **必要的日志记录点**
   - 所有API请求入口和出口点
   - 关键业务操作前后
   - 错误发生时
   - 外部系统调用前后

2. **日志级别使用规范**
   - DEBUG: 详细调试信息
   - INFO: 常规操作信息
   - WARN: 潜在问题或异常情况
   - ERROR: 错误但不影响系统继续运行
   - FATAL: 严重错误导致系统无法继续运行

3. **日志内容要求**
   - 必须包含操作类型
   - 必须包含资源标识符
   - 必须包含关键参数
   - 必须包含错误详情（如果有）
   - 禁止记录敏感信息（密码、令牌等）

## 常见问题与解决方案

### 循环依赖问题

**解决方案**:
- 使用接口解耦
- 重构代码结构，避免循环引用
- 正确使用依赖注入

### HTTP框架与业务逻辑解耦

**解决方案**:
- Handler层不得依赖gin.Context
- 必须使用context.Context传递请求上下文
- 必须显式定义请求参数和返回值

### 数据库操作最佳实践

**解决方案**:
- 使用事务确保操作原子性
- 合理设置索引提高查询效率
- 使用resources.Filter实现复杂查询
- 处理所有可能的数据库错误


## 总结

本规范文档提供了项目架构和开发的主要指南。遵循这些规范可以确保代码的一致性、可维护性和可测试性。开发人员应仔细阅读并遵循这些规范，以保证项目的质量和可持续性。新功能开发应遵循本文档中概述的最佳实践，并与现有代码风格保持一致。