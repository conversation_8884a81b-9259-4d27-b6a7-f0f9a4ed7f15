# Kubernetes RBAC 多租户扩展方案

## 需求概述

基于Kubernetes的RBAC机制，实现一个多租户的权限控制系统，方便用户进行权限管理。本项目旨在解决以下问题：

- 实现平台用户体系与Kubernetes RBAC的无缝对接
- 支持多种实体类型的权限控制（租户、项目、账号、角色）
- 提供标准化的命名规范和自动化的证书管理
- 通过Webhook服务实现灵活的认证和授权机制

## 方案概述

基于Kubernetes原生RBAC实现的多租户权限控制系统，通过标准化命名规范和扩展Webhook实现：
- 租户/项目 → 使用 `Group` 类型 + 前缀标识
- 平台账号/角色 → 使用 `User` 类型 + 前缀标识
- 通过准入控制器维护命名合规性和权限逻辑

## 详细设计

### 架构

系统由以下几个核心组件组成：

1. **Token验证Webhook服务**：处理Kubernetes的认证请求，验证token并返回用户信息
2. **证书管理工具**：生成和管理用户证书，用于TLS认证
3. **RBAC资源管理**：创建和维护ClusterRole、Role、ClusterRoleBinding和RoleBinding资源
4. **平台用户数据管理**：管理租户、项目、账号和角色信息

系统架构图：

```mermaid
graph TB
  subgraph Kubernetes
    API_Server -->|认证请求| Webhook
    Webhook -->|认证结果| API_Server
    API_Server -->|授权检查| RBAC
  end

  subgraph 平台组件
    UserService[用户服务] -->|用户数据| Webhook
    CertManager[证书管理] -->|签发证书| Kubernetes
  end

  subgraph 客户端
    User -->|访问API| API_Server
    User -->|获取证书| CertManager
  end
```

### 数据结构

#### 平台实体模型

```go
// PlatformType 表示平台实体类型
type PlatformType string

const (
    // TypeTenant 租户类型
    TypeTenant PlatformType = "tenant"
    // TypeProject 项目类型
    TypeProject PlatformType = "project"
    // TypeAccount 平台账号类型
    TypeAccount PlatformType = "account"
    // TypeRole 平台角色类型
    TypeRole PlatformType = "role"
)

// PlatformEntity 表示平台实体
type PlatformEntity struct {
    Type        PlatformType       `json:"type"`
    ID          string             `json:"id"`
    Name        string             `json:"name"`
    Description string             `json:"description,omitempty"`
    Metadata    map[string]string  `json:"metadata,omitempty"`
}
```

#### RBAC映射关系

平台实体类型与Kubernetes RBAC的映射关系：

| 平台实体类型 | Kubernetes RBAC类型 | 权限范围      | 映射示例                    |
|------------|-------------------|-------------|----------------------------|
| 租户        | Group             | 集群级       | ClusterRoleBinding到租户组  |
| 项目        | Group             | 命名空间级    | RoleBinding到项目组         |
| 账号        | User              | 命名空间级    | RoleBinding到用户           |
| 角色        | User              | 集群级       | ClusterRoleBinding到角色    |

### 核心流程

#### RBAC映射流程

1. **平台用户体系到Kubernetes Subject的映射**：
   - 租户和项目映射为Kubernetes Group
   - 账号和角色映射为Kubernetes User
   - 统一使用前缀`rbac.hmc.cn`进行标识

2. **权限设计和映射**：
   - 租户管理员：对所有关联项目有管理权限，通过ClusterRoleBinding实现
   - 项目成员：仅对特定项目有权限，通过RoleBinding实现
   - 平台角色：根据角色定义的权限范围，通过ClusterRoleBinding实现
   - 平台账号：根据关联的项目，通过RoleBinding实现

3. **权限继承关系**：
   - 账号可以继承所属项目的权限
   - 项目可以继承所属租户的权限
   - 通过metadata中的关联信息建立实体间的层级关系

#### 证书签发流程

证书签发采用Kubernetes CSR (CertificateSigningRequest) 机制，流程如下：

1. **生成密钥对**：为每个实体生成RSA密钥对（默认2048位）
2. **创建CSR**：
   - 设置CommonName为实体的Kubernetes名称（如`rbac.hmc.cn:account:user001`）
   - 对于Group类型（租户和项目），在Organization字段设置组名称
   - 对于User类型（账号和角色），Organization设置为默认值（如`rbac-demo`）
3. **提交CSR到Kubernetes**：
   - 设置SignerName为`kubernetes.io/kube-apiserver-client`
   - 设置证书用途（KeyUsage）为客户端认证
   - 设置有效期为365天
4. **自动批准CSR**：系统自动批准CSR请求
5. **等待证书签发**：轮询等待Kubernetes签发证书
6. **生成kubeconfig**：
   - 将签发的证书和私钥嵌入kubeconfig文件
   - 设置适当的上下文和集群信息

**时序图**：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant CertManager as 证书管理器
    participant K8s as Kubernetes API
    
    Client->>CertManager: 请求为实体生成证书
    CertManager->>CertManager: 生成RSA密钥对
    CertManager->>CertManager: 创建CSR（设置CN和O）
    CertManager->>K8s: 提交CSR
    K8s-->>CertManager: 返回CSR资源
    CertManager->>K8s: 批准CSR
    K8s-->>CertManager: 确认批准
    loop 等待签发
        CertManager->>K8s: 查询CSR状态
        K8s-->>CertManager: 返回状态
    end
    CertManager->>CertManager: 生成kubeconfig
    CertManager-->>Client: 返回kubeconfig文件
```

#### 安全考量

使用证书进行认证存在以下安全风险，需要特别注意：

1. **证书绕过Webhook验证**：通过证书签发的kubeconfig文件在认证时不会经过Webhook服务，这意味着：
   - 不会实时校验用户、角色、组织或项目是否仍然存在
   - 如果平台中删除了某个实体，其对应的证书仍然有效，直到证书过期

2. **证书吊销问题**：
   - Kubernetes目前没有内置完善的证书吊销机制
   - 如果证书被泄露或用户权限被撤销，已签发的证书在过期前仍然有效
   - 实体被删除后，无法立即吊销其证书，存在安全风险

3. **已实施的缓解措施**：
   - **缩短证书有效期至7天**：通过修改CSR创建时的ExpirationSeconds参数，将证书有效期从365天缩短至7天
   ```go
   // 设置证书有效期为7天
   expirationSeconds := int32(7 * 24 * 60 * 60)
   csr.Spec.ExpirationSeconds = &expirationSeconds
   ```
   
   - **用户删除时自动清理RBAC绑定**：实现webhook回调接口，当平台删除用户时，自动删除该用户在Kubernetes中的所有RoleBinding和ClusterRoleBinding
   ```go
   // 用户删除回调接口示例
   func (h *UserHandler) DeleteUserCallback(c *gin.Context) {
       // 获取被删除的用户ID
       userID := c.Param("id")
       
       // 构建用户在Kubernetes中的名称
       username := fmt.Sprintf("%s:%s:%s", model.PlatformPrefix, model.TypeAccount, userID)
       
       // 删除所有与该用户相关的RoleBinding和ClusterRoleBinding
       if err := h.rbacService.CleanupUserBindings(username); err != nil {
           klog.Errorf("清理用户%s的RBAC绑定失败: %v", username, err)
       }
       
       c.JSON(http.StatusOK, gin.H{"message": "用户及其权限已删除"})
   }
   ```

4. **其他建议的缓解措施**：
   - 实现证书定期轮换机制
   - 考虑使用ValidatingAdmissionWebhook进行额外的权限检查
   - 维护证书黑名单，在关键操作前检查证书状态

5. **最佳实践**：
   - 对敏感操作实施多因素认证
   - 定期审计证书使用情况
   - 实现证书使用情况的监控和告警
   - 考虑使用外部身份提供商（如OIDC）作为补充认证机制

在生产环境中，应根据安全需求选择适当的认证方式，并考虑证书和Token认证的组合使用。

#### Token验证流程

1. **客户端请求**：客户端携带token请求Kubernetes API
2. **API服务器转发**：Kubernetes API服务器将token转发到webhook服务
3. **token解析**：webhook服务解析token（格式为`type:id`）
4. **实体查找**：根据token中的类型和ID查找对应的平台实体
5. **用户信息构造**：构造包含用户名、UID和组信息的认证响应
6. **返回结果**：将认证结果返回给Kubernetes API服务器

**时序图**：

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as Kubernetes API
    participant Webhook as Token Webhook
    participant Store as 用户数据存储
    
    Client->>API: 请求资源（携带token）
    API->>Webhook: 转发TokenReview请求
    Webhook->>Webhook: 解析token
    Webhook->>Store: 查找实体信息
    Store-->>Webhook: 返回实体数据
    Webhook->>Webhook: 构造用户信息和组关系
    Webhook-->>API: 返回TokenReview响应
    API-->>Client: 返回资源或拒绝
```

## 命名规范

| 实体类型       | Kind    | 名称格式示例                   |
|----------------|---------|--------------------------------|
| 租户           | Group   | `rbac.hmc.cn:tenant:<租户ID>`  |
| 项目           | Group   | `rbac.hmc.cn:project:<项目ID>` |
| 平台账号       | User    | `rbac.hmc.cn:account:<用户ID>` |
| 平台角色       | User    | `rbac.hmc.cn:role:<角色名>`    |

## RBAC绑定示例

```yaml
# 租户级绑定
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tenant-admin
subjects:
- kind: Group
  name: "rbac.hmc.cn:tenant:finance"
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: ClusterRole
  name: tenant-admin-role
```

```yaml
# 项目级绑定
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: project-dev
  namespace: project-b-ns
subjects:
- kind: Group
  name: "rbac.hmc.cn:project:project-b"
- kind: User
  name: "rbac.hmc.cn:account:user001"
roleRef:
  kind: Role
  name: developer
```

## Webhook 实现

### 功能列表
1. **命名合规校验**
   - 拒绝不符合前缀规范的Subject创建
2. **逻辑关系校验**
   - 防止租户角色绑定到项目组等非法操作
3. **自动权限映射**
   - 平台角色到实际ClusterRole的自动转换

### 实现代码示例

Token验证的核心代码：

```go
// ValidateToken 验证token并返回用户信息
func (s *TokenService) ValidateToken(token string) (string, string, []string, error) {
    // 解析token（格式：type:id）
    parts := strings.Split(token, ":")
    if len(parts) != 2 {
        return "", "", nil, errors.New("无效的token格式")
    }

    entityType := parts[0]
    entityID := parts[1]

    switch model.PlatformType(entityType) {
    case model.TypeTenant:
        // 查找租户
        for _, tenant := range s.platformData.Tenants {
            if tenant.ID == entityID {
                username := tenant.GetKubernetesName()
                uid := tenant.ID
                groups := []string{tenant.GetKubernetesName()}
                return username, uid, groups, nil
            }
        }
    case model.TypeAccount:
        // 查找平台账号
        for _, account := range s.platformData.Accounts {
            if account.ID == entityID {
                username := account.GetKubernetesName()
                uid := account.ID
                // 查找该账号关联的项目和租户
                groups := findAccountGroups(account.ID, s.platformData)
                return username, uid, groups, nil
            }
        }
    // ... 其他类型处理 ...
    }

    return "", "", nil, errors.New("实体不存在")
}
```

## 接口设计

[注: 本节为预留部分，将根据后续提供的Figma PRD设计进行补充完善]

### 当前支持的接口

#### 认证接口

- **接口地址**: `/openapi/token/webhook`
- **方法**: POST
- **功能**: 处理Kubernetes的TokenReview请求，验证token并返回用户信息
- **请求格式**: Kubernetes TokenReview资源
- **响应格式**: Kubernetes TokenReview资源，包含认证结果

### 未来计划扩展的接口

未来将根据Figma PRD设计扩展以下接口：

- 用户管理接口
- 租户管理接口
- 项目管理接口
- 角色权限管理接口
- 证书管理接口

## 部署要求

### 前置依赖
- Kubernetes 1.19+（支持ValidatingWebhookConfiguration）

### 部署步骤
1. 部署Webhook服务
```bash
helm install rbac-webhook ./charts/webhook \
  --set domain=hmc.cn
```

## 外部依赖

| 依赖组件       | 版本要求      | 用途                           |
|---------------|--------------|--------------------------------|
| Kubernetes    | 1.19+        | 提供RBAC和CSR基础设施           |
| Gin           | 1.8.1+       | Web框架，用于实现Webhook服务     |
| cert-manager  | 1.5.0+       | 可选，用于自动化证书管理         |
