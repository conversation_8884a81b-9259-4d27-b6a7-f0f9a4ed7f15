package workload

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/project/workload"
	workloadmodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workload"
	commonRouter "harmonycloud.cn/unifiedportal/portal/backend/pkg/router/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/labels"
)

func NewWorkloadController() routerutil.ApiController {
	return &routeWorkloadController{
		DeploymentHandler:  workload.NewDeploymentHandler(),
		StatefulSetHandler: workload.NewStatefulSetHandler(),
		DaemonSetHandler:   workload.NewDaemonSetHandler(),
		JobHandler:         workload.NewJobHandler(),
		CronJobHandler:     workload.NewCronJobHandler(),
	}
}

type routeWorkloadController struct {
	DeploymentHandler  workload.DeploymentHandler
	StatefulSetHandler workload.StatefulSetHandler
	DaemonSetHandler   workload.DaemonSetHandler
	JobHandler         workload.JobHandler
	CronJobHandler     workload.CronJobHandler
}

func (rwc *routeWorkloadController) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group("/workloads")
}

func (rwc *routeWorkloadController) RegisterAppsRouter(routes *gin.RouterGroup) {
	// get
	routes.GET("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype/:workloadName", rwc.getWorkload)
	// list
	routes.GET("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype", rwc.listWorkloads)

	// write

	// create
	routes.POST("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype", rwc.createWorkload)
	// update
	routes.PUT("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype/:workloadName", rwc.updateWorkload)
	// patch
	routes.PATCH("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype/:workloadName", rwc.patchWorkload)
	// delete
	routes.DELETE("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype/:workloadName", rwc.deleteWorkload)
}

func (rwc *routeWorkloadController) RegisterBatchRouter(routes *gin.RouterGroup) {
	// get
	routes.GET("/clusters/:cluster/apis/batch/v1/namespaces/:namespace/:workloadtype/:workloadName", rwc.getWorkload)
	// list
	routes.GET("/clusters/:cluster/apis/batch/v1/namespaces/:namespace/:workloadtype", rwc.listWorkloads)
	// write

	// create
	routes.POST("/clusters/:cluster/apis/batch/v1/namespaces/:namespace/:workloadtype", rwc.createWorkload)
	// update
	routes.PUT("/clusters/:cluster/apis/batch/v1/namespaces/:namespace/:workloadtype/:workloadName", rwc.updateWorkload)
	// patch
	routes.PATCH("/clusters/:cluster/apis/batch/v1/namespaces/:namespace/:workloadtype/:workloadName", rwc.patchWorkload)
	// delete
	routes.DELETE("/clusters/:cluster/apis/batch/v1/namespaces/:namespace/:workloadtype/:workloadName", rwc.deleteWorkload)
}

func (rwc *routeWorkloadController) RegisterRouter(routes *gin.RouterGroup) {
	rwc.RegisterAppsRouter(routes)
	rwc.RegisterBatchRouter(routes)
}

func (rwc *routeWorkloadController) getWorkload(c *gin.Context) {
	p := &common.ReadParam{}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	p.Filter = utils.ParseQueryParams[unstructured.Unstructured](c)
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DeploymentHandler.Get(c, &workloadmodels.GetDeploymentRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.StatefulSetHandler.Get(c, &workloadmodels.GetStatefulSetRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeDaemonSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DaemonSetHandler.Get(c, &workloadmodels.GetDaemonSetRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.JobHandler.Get(c, &workloadmodels.GetJobRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeCronJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.CronJobHandler.Get(c, &workloadmodels.GetCronJobRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	default:
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sError, fmt.Sprintf("unsupported workload type: %s", p.ResourceType)))
	}
}

func (rwc *routeWorkloadController) listWorkloads(c *gin.Context) {
	p := &common.ReadParam{}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	parse, err := labels.Parse(c.Param("labelSelector"))
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.LabelSelector = parse
	p.Filter = utils.ParseQueryParams[unstructured.Unstructured](c)
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DeploymentHandler.List(c, &workloadmodels.ListDeploymentRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.StatefulSetHandler.List(c, &workloadmodels.ListStatefulSetRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeDaemonSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DaemonSetHandler.List(c, &workloadmodels.ListDaemonSetRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.JobHandler.List(c, &workloadmodels.ListJobRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeCronJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.CronJobHandler.List(c, &workloadmodels.ListCronJobRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	default:
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sError, fmt.Sprintf("unsupported workload type: %s", p.ResourceType)))
	}
}

func (rwc *routeWorkloadController) createWorkload(c *gin.Context) {
	p := &common.WriteParam{
		DryRun: c.Query("dryRun") == "true",
	}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	var err error
	p.Raw, err = c.GetRawData()
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.ContentType = c.ContentType()
	if p.Namespace != "" {
		if err := commonRouter.AuthProjectPermission(c, p.Cluster, p.Namespace, c.GetHeader(commonRouter.HandlerOrgan), c.GetHeader(commonRouter.HandlerProject)); err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
	}
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DeploymentHandler.Create(c, &workloadmodels.CreateDeploymentRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.StatefulSetHandler.Create(c, &workloadmodels.CreateStatefulSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeDaemonSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DaemonSetHandler.Create(c, &workloadmodels.CreateDaemonSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.JobHandler.Create(c, &workloadmodels.CreateJobRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeCronJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.CronJobHandler.Create(c, &workloadmodels.CreateCronJobRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	default:
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sError, fmt.Sprintf("unsupported workload type: %s", p.ResourceType)))
	}
}

func (rwc *routeWorkloadController) updateWorkload(c *gin.Context) {
	p := &common.WriteParam{}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	var err error
	p.Raw, err = c.GetRawData()
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.ContentType = c.ContentType()
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DeploymentHandler.Update(c, &workloadmodels.UpdateDeploymentRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.StatefulSetHandler.Update(c, &workloadmodels.UpdateStatefulSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeDaemonSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DaemonSetHandler.Update(c, &workloadmodels.UpdateDaemonSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.JobHandler.Update(c, &workloadmodels.UpdateJobRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeCronJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.CronJobHandler.Update(c, &workloadmodels.UpdateCronJobRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	default:
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sError, fmt.Sprintf("unsupported workload type: %s", p.ResourceType)))
	}
}

func (rwc *routeWorkloadController) patchWorkload(c *gin.Context) {
	p := &common.WriteParam{}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	var err error
	p.Raw, err = c.GetRawData()
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.ContentType = c.ContentType()
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DeploymentHandler.Patch(c, &workloadmodels.PatchDeploymentRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.StatefulSetHandler.Patch(c, &workloadmodels.PatchStatefulSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeDaemonSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DaemonSetHandler.Patch(c, &workloadmodels.PatchDaemonSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.JobHandler.Patch(c, &workloadmodels.PatchJobRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeCronJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.CronJobHandler.Patch(c, &workloadmodels.PatchCronJobRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	default:
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sError, fmt.Sprintf("unsupported workload type: %s", p.ResourceType)))
	}
}

func (rwc *routeWorkloadController) deleteWorkload(c *gin.Context) {
	p := &common.WriteParam{}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DeploymentHandler.Delete(c, &workloadmodels.DeleteDeploymentRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.StatefulSetHandler.Delete(c, &workloadmodels.DeleteStatefulSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeDaemonSets:
		p.Group = appsv1.SchemeGroupVersion.Group
		p.Version = appsv1.SchemeGroupVersion.Version
		response, err := rwc.DaemonSetHandler.Delete(c, &workloadmodels.DeleteDaemonSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.JobHandler.Delete(c, &workloadmodels.DeleteJobRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeCronJobs:
		p.Group = batchv1.SchemeGroupVersion.Group
		p.Version = batchv1.SchemeGroupVersion.Version
		response, err := rwc.CronJobHandler.Delete(c, &workloadmodels.DeleteCronJobRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	default:
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sError, fmt.Sprintf("unsupported workload type: %s", p.ResourceType)))
	}
}
