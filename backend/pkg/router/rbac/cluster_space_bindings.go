package rbac

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	rbacModels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

// ===== RoleBinding 相关路由处理器 =====

// CreateRoleBinding 创建角色绑定
// @Summary 创建角色绑定
// @Description 在指定集群和命名空间中创建角色绑定
// @Tags RBAC-RoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param body body rbacModels.CreateRoleBindingRequest true "创建角色绑定请求"
// @Success 200 {object} rbacModels.CreateRoleBindingResponse
// @Failure 400 {object} router.ErrorResponse
// @Failure 409 {object} router.ErrorResponse "角色绑定已存在"
// @Router /apis/v1/clusters/{cluster_id}/namespaces/{namespace}/rolebindings [post]
func (r *ClusterSpaceRouter) CreateRoleBinding(c *gin.Context) {
	var req rbacModels.CreateRoleBindingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	// 从路径参数获取集群ID和命名空间
	req.ClusterID = c.Param("cluster_id")
	req.Namespace = c.Param("namespace")

	resp, err := r.handler.CreateRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// GetRoleBinding 获取角色绑定详情
// @Summary 获取角色绑定详情
// @Description 获取指定角色绑定的详细信息
// @Tags RBAC-RoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "角色绑定名称"
// @Success 200 {object} rbacModels.GetRoleBindingResponse
// @Failure 404 {object} router.ErrorResponse "角色绑定不存在"
// @Router /apis/v1/clusters/{cluster_id}/namespaces/{namespace}/rolebindings/{name} [get]
func (r *ClusterSpaceRouter) GetRoleBinding(c *gin.Context) {
	req := rbacModels.GetRoleBindingRequest{
		ClusterID: c.Param("cluster_id"),
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.GetRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ListRoleBinding 获取角色绑定列表
// @Summary 获取角色绑定列表
// @Description 获取指定集群和命名空间的角色绑定列表
// @Tags RBAC-RoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Success 200 {object} rbacModels.ListRoleBindingResponse
// @Router /apis/v1/clusters/{cluster_id}/namespaces/{namespace}/rolebindings [get]
func (r *ClusterSpaceRouter) ListRoleBinding(c *gin.Context) {
	req := rbacModels.ListRoleBindingRequest{
		ClusterID: c.Param("cluster_id"),
		Namespace: c.Param("namespace"),
	}

	// 设置过滤器
	req.Filter = utils.ParseQueryParams[*rbacModels.RoleBinding](c)

	resp, err := r.handler.ListRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// UpdateRoleBinding 更新角色绑定
// @Summary 更新角色绑定
// @Description 更新指定角色绑定的信息
// @Tags RBAC-RoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "角色绑定名称"
// @Param body body rbacModels.UpdateRoleBindingRequest true "更新角色绑定请求"
// @Success 200 {object} rbacModels.UpdateRoleBindingResponse
// @Failure 404 {object} router.ErrorResponse "角色绑定不存在"
// @Router /apis/v1/clusters/{cluster_id}/namespaces/{namespace}/rolebindings/{name} [put]
func (r *ClusterSpaceRouter) UpdateRoleBinding(c *gin.Context) {
	var req rbacModels.UpdateRoleBindingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	req.ClusterID = c.Param("cluster_id")
	req.Namespace = c.Param("namespace")
	req.Name = c.Param("name")

	resp, err := r.handler.UpdateRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// DeleteRoleBinding 删除角色绑定
// @Summary 删除角色绑定
// @Description 删除指定的角色绑定
// @Tags RBAC-RoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "角色绑定名称"
// @Success 200 {object} rbacModels.DeleteRoleBindingResponse
// @Failure 404 {object} router.ErrorResponse "角色绑定不存在"
// @Router /apis/v1/clusters/{cluster_id}/namespaces/{namespace}/rolebindings/{name} [delete]
func (r *ClusterSpaceRouter) DeleteRoleBinding(c *gin.Context) {
	req := rbacModels.DeleteRoleBindingRequest{
		ClusterID: c.Param("cluster_id"),
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.DeleteRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ===== ClusterRoleBinding 相关路由处理器 =====

// CreateClusterRoleBinding 创建集群角色绑定
// @Summary 创建集群角色绑定
// @Description 在指定集群中创建集群角色绑定
// @Tags RBAC-ClusterRoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param body body rbacModels.CreateClusterRoleBindingRequest true "创建集群角色绑定请求"
// @Success 200 {object} rbacModels.CreateClusterRoleBindingResponse
// @Failure 400 {object} router.ErrorResponse
// @Failure 409 {object} router.ErrorResponse "集群角色绑定已存在"
// @Router /apis/v1/clusters/{cluster_id}/clusterrolebindings [post]
func (r *ClusterSpaceRouter) CreateClusterRoleBinding(c *gin.Context) {
	var req rbacModels.CreateClusterRoleBindingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	// 从路径参数获取集群ID
	req.ClusterID = c.Param("cluster_id")

	resp, err := r.handler.CreateClusterRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// GetClusterRoleBinding 获取集群角色绑定详情
// @Summary 获取集群角色绑定详情
// @Description 获取指定集群角色绑定的详细信息
// @Tags RBAC-ClusterRoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param name path string true "集群角色绑定名称"
// @Success 200 {object} rbacModels.GetClusterRoleBindingResponse
// @Failure 404 {object} router.ErrorResponse "集群角色绑定不存在"
// @Router /apis/v1/clusters/{cluster_id}/clusterrolebindings/{name} [get]
func (r *ClusterSpaceRouter) GetClusterRoleBinding(c *gin.Context) {
	req := rbacModels.GetClusterRoleBindingRequest{
		ClusterID: c.Param("cluster_id"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.GetClusterRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ListClusterRoleBinding 获取集群角色绑定列表
// @Summary 获取集群角色绑定列表
// @Description 获取指定集群的集群角色绑定列表
// @Tags RBAC-ClusterRoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Success 200 {object} rbacModels.ListClusterRoleBindingResponse
// @Router /apis/v1/clusters/{cluster_id}/clusterrolebindings [get]
func (r *ClusterSpaceRouter) ListClusterRoleBinding(c *gin.Context) {
	req := rbacModels.ListClusterRoleBindingRequest{
		ClusterID: c.Param("cluster_id"),
	}

	// 设置过滤器
	req.Filter = utils.ParseQueryParams[*rbacModels.ClusterRoleBinding](c)

	resp, err := r.handler.ListClusterRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// UpdateClusterRoleBinding 更新集群角色绑定
// @Summary 更新集群角色绑定
// @Description 更新指定集群角色绑定的信息
// @Tags RBAC-ClusterRoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param name path string true "集群角色绑定名称"
// @Param body body rbacModels.UpdateClusterRoleBindingRequest true "更新集群角色绑定请求"
// @Success 200 {object} rbacModels.UpdateClusterRoleBindingResponse
// @Failure 404 {object} router.ErrorResponse "集群角色绑定不存在"
// @Router /apis/v1/clusters/{cluster_id}/clusterrolebindings/{name} [put]
func (r *ClusterSpaceRouter) UpdateClusterRoleBinding(c *gin.Context) {
	var req rbacModels.UpdateClusterRoleBindingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	req.ClusterID = c.Param("cluster_id")
	req.Name = c.Param("name")

	resp, err := r.handler.UpdateClusterRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// DeleteClusterRoleBinding 删除集群角色绑定
// @Summary 删除集群角色绑定
// @Description 删除指定的集群角色绑定
// @Tags RBAC-ClusterRoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param name path string true "集群角色绑定名称"
// @Success 200 {object} rbacModels.DeleteClusterRoleBindingResponse
// @Failure 404 {object} router.ErrorResponse "集群角色绑定不存在"
// @Router /apis/v1/clusters/{cluster_id}/clusterrolebindings/{name} [delete]
func (r *ClusterSpaceRouter) DeleteClusterRoleBinding(c *gin.Context) {
	req := rbacModels.DeleteClusterRoleBindingRequest{
		ClusterID: c.Param("cluster_id"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.DeleteClusterRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ValidateClusterRoleBindingName 校验集群角色绑定名称
// @Summary 校验集群角色绑定名称
// @Description 校验集群角色绑定名称是否可用
// @Tags RBAC-ClusterRoleBinding
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param name query string true "需要校验的集群角色绑定名称"
// @Success 200 {object} rbacModels.ValidateClusterRoleBindingNameResponse
// @Failure 409 {object} router.ErrorResponse "集群角色绑定名称已存在"
// @Router /apis/v1/clusters/{cluster_id}/clusterrolebindings/validate [get]
func (r *ClusterSpaceRouter) ValidateClusterRoleBindingName(c *gin.Context) {
	req := rbacModels.ValidateClusterRoleBindingNameRequest{
		ClusterID: c.Param("clusterId"),
		Name:      c.Query("name"),
	}

	resp, err := r.handler.ValidateClusterRoleBindingName(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}
