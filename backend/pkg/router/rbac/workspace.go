package rbac

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/rbac"
	rbacModels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

// WorkspaceRouter 工作空间RBAC路由
type WorkspaceRouter struct {
	handler rbac.Handler
}

// NewWorkspaceRouter 创建工作空间RBAC路由
func NewWorkspaceRouter() *WorkspaceRouter {
	return &WorkspaceRouter{
		handler: rbac.NewHandler(),
	}
}

// RegisterRoutes 注册工作空间RBAC路由
func (r *WorkspaceRouter) RegisterRoutes(rg *gin.RouterGroup) {
	// 集群级别的工作空间Role路由
	clusterGroup := rg.Group("/clusters/:cluster_id")
	{
		// 工作空间Role相关路由
		roleGroup := clusterGroup.Group("/roles")
		{
			roleGroup.GET("", r.ListWorkspaceRole)
		}

		// 命名空间级别的工作空间操作
		namespaceGroup := clusterGroup.Group("/namespaces/:namespace")
		{
			// 角色操作
			roleGroup := namespaceGroup.Group("/roles")
			{
				roleGroup.GET("/:name", r.GetWorkspaceRole)
				roleGroup.POST("", r.CreateWorkspaceRole)
				roleGroup.PUT("/:name", r.UpdateWorkspaceRole)
				roleGroup.DELETE("/:name", r.DeleteWorkspaceRole)
			}

			// 角色名称校验
			namespaceGroup.GET("/roles/validate", r.ValidateWorkspaceRoleName)

			// RoleBinding相关路由
			roleBindingGroup := namespaceGroup.Group("/rolebindings")
			{
				roleBindingGroup.GET("", r.ListWorkspaceRoleBinding)
				roleBindingGroup.GET("/:name", r.GetWorkspaceRoleBinding)
				roleBindingGroup.POST("", r.CreateWorkspaceRoleBinding)
				roleBindingGroup.PUT("/:name", r.UpdateWorkspaceRoleBinding)
				roleBindingGroup.DELETE("/:name", r.DeleteWorkspaceRoleBinding)
			}

			// RoleBinding名称校验
			namespaceGroup.GET("/rolebindings/validate", r.ValidateWorkspaceRoleBindingName)

			// ServiceAccount相关路由
			serviceAccountGroup := namespaceGroup.Group("/serviceaccounts")
			{
				serviceAccountGroup.GET("/:name", r.GetWorkspaceServiceAccount)
				serviceAccountGroup.POST("", r.CreateWorkspaceServiceAccount)
				serviceAccountGroup.PUT("/:name", r.UpdateWorkspaceServiceAccount)
				serviceAccountGroup.DELETE("/:name", r.DeleteWorkspaceServiceAccount)
			}

			// ServiceAccount名称校验
			namespaceGroup.POST("/serviceaccounts/validate", r.ValidateWorkspaceServiceAccountName)

			// 可绑定角色路由
			namespaceGroup.GET("/bindable-roles", r.GetBindableRoles)
		}

		// ServiceAccount列表查询（集群级别）
		clusterGroup.GET("/serviceaccounts", r.ListWorkspaceServiceAccount)
	}
}

// ===== 工作空间Role 相关路由处理器 =====

// ListWorkspaceRole 获取工作空间角色列表
// @Summary 获取工作空间角色列表
// @Description 获取指定工作空间的角色列表
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organization_id path string true "组织ID"
// @Param project_id path string true "项目ID"
// @Param cluster_id path string true "集群ID"
// @Param namespace query string false "命名空间"
// @Success 200 {object} rbacModels.ListRoleResponse
// @Router /apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/roles [get]
func (r *WorkspaceRouter) ListWorkspaceRole(c *gin.Context) {
	req := rbacModels.WorkspaceListRoleRequest{
		WorkspaceRoleRequest: rbacModels.WorkspaceRoleRequest{
			OrganizationID: c.Param("organization_id"),
			ProjectID:      c.Param("project_id"),
			ClusterID:      c.Param("cluster_id"),
		},
		Namespace: c.Query("namespace"),
	}

	// 设置过滤器
	req.Filter = utils.ParseQueryParams[*rbacModels.Role](c)

	resp, err := r.handler.ListWorkspaceRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// GetWorkspaceRole 获取工作空间角色详情
// @Summary 获取工作空间角色详情
// @Description 获取指定工作空间角色的详细信息
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organization_id path string true "组织ID"
// @Param project_id path string true "项目ID"
// @Param cluster_id path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "角色名称"
// @Success 200 {object} rbacModels.GetRoleResponse
// @Failure 404 {object} router.ErrorResponse "角色不存在"
// @Router /apis/v1/organizations/{organization_id}/projects/{project_id}/clusters/{cluster_id}/namespaces/{namespace}/roles/{name} [get]
func (r *WorkspaceRouter) GetWorkspaceRole(c *gin.Context) {
	req := rbacModels.WorkspaceGetRoleRequest{
		WorkspaceRoleRequest: rbacModels.WorkspaceRoleRequest{
			OrganizationID: c.Param("organization_id"),
			ProjectID:      c.Param("project_id"),
			ClusterID:      c.Param("cluster_id"),
		},
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.GetWorkspaceRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// CreateWorkspaceRole 创建工作空间角色
// @Summary 创建工作空间角色
// @Description 在指定工作空间中创建角色
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param body body rbacModels.WorkspaceCreateRoleRequest true "创建角色请求"
// @Success 200 {object} rbacModels.CreateRoleResponse
// @Failure 400 {object} router.ErrorResponse
// @Failure 409 {object} router.ErrorResponse "角色已存在"
// @Router /api/v1/organizations/{organizationId}/projects/{projectId}/rbac/roles [post]
func (r *WorkspaceRouter) CreateWorkspaceRole(c *gin.Context) {
	var req rbacModels.WorkspaceCreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	// 从路径参数获取工作空间信息
	req.OrganizationID = c.Param("organization_id")
	req.ProjectID = c.Param("project_id")
	req.ClusterID = c.Param("cluster_id")
	req.Namespace = c.Param("namespace")

	resp, err := r.handler.CreateWorkspaceRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// UpdateWorkspaceRole 更新工作空间角色
// @Summary 更新工作空间角色
// @Description 更新指定工作空间角色的信息
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "角色名称"
// @Param body body rbacModels.Role true "角色信息"
// @Success 200 {object} rbacModels.UpdateRoleResponse
// @Failure 404 {object} router.ErrorResponse "角色不存在"
// @Router /api/v1/organizations/{organizationId}/projects/{projectId}/rbac/roles/{name} [put]
func (r *WorkspaceRouter) UpdateWorkspaceRole(c *gin.Context) {
	var role rbacModels.Role
	if err := c.ShouldBindJSON(&role); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	req := rbacModels.WorkspaceUpdateRoleRequest{
		WorkspaceRoleRequest: rbacModels.WorkspaceRoleRequest{
			OrganizationID: c.Param("organization_id"),
			ProjectID:      c.Param("project_id"),
			ClusterID:      c.Param("cluster_id"),
		},
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
		Role:      &role,
	}

	resp, err := r.handler.UpdateWorkspaceRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// DeleteWorkspaceRole 删除工作空间角色
// @Summary 删除工作空间角色
// @Description 删除指定的工作空间角色
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "角色名称"
// @Success 200 {object} rbacModels.DeleteRoleResponse
// @Failure 404 {object} router.ErrorResponse "角色不存在"
// @Failure 409 {object} router.ErrorResponse "角色正在被使用"
// @Router /api/v1/organizations/{organizationId}/projects/{projectId}/rbac/roles/{name} [delete]
func (r *WorkspaceRouter) DeleteWorkspaceRole(c *gin.Context) {
	req := rbacModels.WorkspaceDeleteRoleRequest{
		WorkspaceRoleRequest: rbacModels.WorkspaceRoleRequest{
			OrganizationID: c.Param("organization_id"),
			ProjectID:      c.Param("project_id"),
			ClusterID:      c.Param("cluster_id"),
		},
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.DeleteWorkspaceRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ValidateWorkspaceRoleName 校验工作空间角色名称
// @Summary 校验工作空间角色名称
// @Description 校验工作空间角色名称在指定命名空间内是否可用
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name query string true "需要校验的角色名称"
// @Success 200 {object} rbacModels.ValidateRoleNameResponse
// @Failure 409 {object} router.ErrorResponse "角色名称已存在"
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/roles/validate [get]
func (r *WorkspaceRouter) ValidateWorkspaceRoleName(c *gin.Context) {
	req := rbacModels.ValidateRoleNameRequest{
		ClusterID: c.Param("cluster_id"),
		Namespace: c.Param("namespace"),
		Name:      c.Query("name"),
	}

	resp, err := r.handler.ValidateRoleName(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ===== 工作空间RoleBinding 相关路由处理器 =====

// ListWorkspaceRoleBinding 获取工作空间角色绑定列表
// @Summary 获取工作空间角色绑定列表
// @Description 获取指定工作空间的角色绑定列表
// @Tags RBAC-WorkspaceRoleBinding
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Success 200 {object} rbacModels.ListRoleBindingResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/rolebindings [get]
func (r *WorkspaceRouter) ListWorkspaceRoleBinding(c *gin.Context) {
	req := rbacModels.ListRoleBindingRequest{
		ClusterID: c.Param("cluster_id"),
		Namespace: c.Param("namespace"),
	}

	// 设置过滤器
	req.Filter = utils.ParseQueryParams[*rbacModels.RoleBinding](c)

	resp, err := r.handler.ListRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// GetWorkspaceRoleBinding 获取工作空间角色绑定详情
// @Summary 获取工作空间角色绑定详情
// @Description 获取指定工作空间的角色绑定详情
// @Tags RBAC-WorkspaceRoleBinding
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "角色绑定名称"
// @Success 200 {object} rbacModels.GetRoleBindingResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/rolebindings/{name} [get]
func (r *WorkspaceRouter) GetWorkspaceRoleBinding(c *gin.Context) {
	req := rbacModels.GetRoleBindingRequest{
		ClusterID: c.Param("cluster_id"),
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.GetRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// GetBindableRoles 获取可绑定角色列表
// @Summary 获取可绑定角色列表
// @Description 获取指定集群命名空间中可绑定的角色列表
// @Tags RBAC-WorkspaceRole
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Success 200 {object} rbacModels.GetBindableRolesResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/bindable-roles [get]
func (r *WorkspaceRouter) GetBindableRoles(c *gin.Context) {
	req := rbacModels.GetBindableRolesRequest{
		OrganizationID: c.Param("organization_id"),
		ProjectID:      c.Param("project_id"),
		ClusterID:      c.Param("cluster_id"),
		Namespace:      c.Param("namespace"),
	}

	resp, err := r.handler.GetBindableRoles(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}
