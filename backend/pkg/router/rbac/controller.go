package rbac

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/rbac"
	rbacModels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

// NewRouter 创建RBAC路由控制器
func NewRouter() routerutil.ApiController {
	return &controller{
		handler:            rbac.NewHandler(),
		clusterSpaceRouter: NewClusterSpaceRouter(),
		workspaceRouter:    NewWorkspaceRouter(),
	}
}

// controller RBAC路由控制器
type controller struct {
	handler            rbac.Handler
	clusterSpaceRouter *ClusterSpaceRouter
	workspaceRouter    *WorkspaceRouter
}

// GetGroup 获取路由组
func (c *controller) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group)
}

// RegisterRouter 注册路由
func (c *controller) RegisterRouter(group *gin.RouterGroup) {
	// 集群空间API路由
	clusterGroup := group.Group("/clusters/:cluster_id")
	{
		// 注册集群空间RBAC路由
		c.clusterSpaceRouter.RegisterRoutes(clusterGroup)
	}

	// 工作空间API路由
	workspaceGroup := group.Group("/organizations/:organization_id/projects/:project_id")
	{
		// 注册工作空间RBAC路由
		c.workspaceRouter.RegisterRoutes(workspaceGroup)
	}

	// RBAC主体查询接口
	group.GET("/rbac/subjects", c.getSubjects)
}

// getSubjects 获取RBAC主体列表
// @Summary 获取RBAC主体列表
// @Description 获取平台中可用的RBAC主体（用户、角色、租户、项目等）
// @Tags RBAC-Subject
// @Accept json
// @Produce json
// @Param type query string false "主体类型过滤"
// @Success 200 {object} rbacModels.GetSubjectsResponse
// @Router /apis/v1/rbac/subjects [get]
func (c *controller) getSubjects(ctx *gin.Context) {
	req := &rbacModels.GetSubjectsRequest{
		Type: ctx.Query("type"),
	}

	resp, err := c.handler.GetSubjects(ctx.Request.Context(), req)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx.Request.Context(), err))
		return
	}

	utils.Succeed(ctx, resp)
}
