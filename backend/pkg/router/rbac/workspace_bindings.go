package rbac

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	rbacModels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

// CreateWorkspaceRoleBinding 创建工作空间角色绑定
// @Summary 创建工作空间角色绑定
// @Description 在指定工作空间中创建角色绑定
// @Tags RBAC-WorkspaceRoleBinding
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param body body rbacModels.CreateRoleBindingRequest true "创建角色绑定请求"
// @Success 200 {object} rbacModels.CreateRoleBindingResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/rolebindings [post]
func (r *WorkspaceRouter) CreateWorkspaceRoleBinding(c *gin.Context) {
	var req rbacModels.CreateRoleBindingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	// 从路径参数获取集群ID和命名空间
	req.ClusterID = c.Param("clusterId")
	req.Namespace = c.Param("namespace")

	resp, err := r.handler.CreateRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// UpdateWorkspaceRoleBinding 更新工作空间角色绑定
// @Summary 更新工作空间角色绑定
// @Description 更新指定工作空间的角色绑定
// @Tags RBAC-WorkspaceRoleBinding
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "角色绑定名称"
// @Param body body rbacModels.RoleBinding true "角色绑定对象"
// @Success 200 {object} rbacModels.UpdateRoleBindingResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/rolebindings/{name} [put]
func (r *WorkspaceRouter) UpdateWorkspaceRoleBinding(c *gin.Context) {
	var roleBinding rbacModels.RoleBinding
	if err := c.ShouldBindJSON(&roleBinding); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	req := rbacModels.UpdateRoleBindingRequest{
		ClusterID:   c.Param("clusterId"),
		Namespace:   c.Param("namespace"),
		Name:        c.Param("name"),
		RoleBinding: &roleBinding,
	}

	resp, err := r.handler.UpdateRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// DeleteWorkspaceRoleBinding 删除工作空间角色绑定
// @Summary 删除工作空间角色绑定
// @Description 删除指定工作空间的角色绑定
// @Tags RBAC-WorkspaceRoleBinding
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "角色绑定名称"
// @Success 200 {object} rbacModels.DeleteRoleBindingResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/rolebindings/{name} [delete]
func (r *WorkspaceRouter) DeleteWorkspaceRoleBinding(c *gin.Context) {
	req := rbacModels.DeleteRoleBindingRequest{
		ClusterID: c.Param("clusterId"),
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.DeleteRoleBinding(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ValidateWorkspaceRoleBindingName 校验工作空间角色绑定名称
// @Summary 校验工作空间角色绑定名称
// @Description 校验工作空间角色绑定名称在指定命名空间内是否可用
// @Tags RBAC-WorkspaceRoleBinding
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name query string true "需要校验的角色绑定名称"
// @Success 200 {object} rbacModels.ValidateRoleBindingNameResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/rolebindings/validate [get]
func (r *WorkspaceRouter) ValidateWorkspaceRoleBindingName(c *gin.Context) {
	req := rbacModels.ValidateRoleBindingNameRequest{
		ClusterID: c.Param("clusterId"),
		Namespace: c.Param("namespace"),
		Name:      c.Query("name"),
	}

	resp, err := r.handler.ValidateRoleBindingName(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ===== 工作空间ServiceAccount 相关路由处理器 =====

// ListWorkspaceServiceAccount 获取工作空间服务账号列表
// @Summary 获取工作空间服务账号列表
// @Description 获取指定工作空间的服务账号列表
// @Tags RBAC-WorkspaceServiceAccount
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace query string false "命名空间过滤"
// @Success 200 {object} rbacModels.ListServiceAccountResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/serviceaccounts [get]
func (r *WorkspaceRouter) ListWorkspaceServiceAccount(c *gin.Context) {
	req := rbacModels.ListServiceAccountRequest{
		ClusterID: c.Param("clusterId"),
		Namespace: c.Query("namespace"),
	}

	// 设置过滤器
	req.Filter = utils.ParseQueryParams[*rbacModels.ServiceAccount](c)

	resp, err := r.handler.ListServiceAccount(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// GetWorkspaceServiceAccount 获取工作空间服务账号详情
// @Summary 获取工作空间服务账号详情
// @Description 获取指定工作空间的服务账号详情
// @Tags RBAC-WorkspaceServiceAccount
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "服务账号名称"
// @Success 200 {object} rbacModels.GetServiceAccountResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/serviceaccounts/{name} [get]
func (r *WorkspaceRouter) GetWorkspaceServiceAccount(c *gin.Context) {
	req := rbacModels.GetServiceAccountRequest{
		ClusterID: c.Param("clusterId"),
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.GetServiceAccount(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// CreateWorkspaceServiceAccount 创建工作空间服务账号
// @Summary 创建工作空间服务账号
// @Description 在指定工作空间中创建服务账号
// @Tags RBAC-WorkspaceServiceAccount
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param body body rbacModels.CreateServiceAccountRequest true "创建服务账号请求"
// @Success 200 {object} rbacModels.CreateServiceAccountResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/serviceaccounts [post]
func (r *WorkspaceRouter) CreateWorkspaceServiceAccount(c *gin.Context) {
	var req rbacModels.CreateServiceAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	// 从路径参数获取集群ID和命名空间
	req.ClusterID = c.Param("clusterId")
	req.Namespace = c.Param("namespace")

	resp, err := r.handler.CreateServiceAccount(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// UpdateWorkspaceServiceAccount 更新工作空间服务账号
// @Summary 更新工作空间服务账号
// @Description 更新指定工作空间的服务账号
// @Tags RBAC-WorkspaceServiceAccount
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "服务账号名称"
// @Param body body rbacModels.ServiceAccount true "服务账号对象"
// @Success 200 {object} rbacModels.UpdateServiceAccountResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/serviceaccounts/{name} [put]
func (r *WorkspaceRouter) UpdateWorkspaceServiceAccount(c *gin.Context) {
	var serviceAccount rbacModels.ServiceAccount
	if err := c.ShouldBindJSON(&serviceAccount); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	req := rbacModels.UpdateServiceAccountRequest{
		ClusterID:      c.Param("clusterId"),
		Namespace:      c.Param("namespace"),
		Name:           c.Param("name"),
		ServiceAccount: &serviceAccount,
	}

	resp, err := r.handler.UpdateServiceAccount(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// DeleteWorkspaceServiceAccount 删除工作空间服务账号
// @Summary 删除工作空间服务账号
// @Description 删除指定工作空间的服务账号
// @Tags RBAC-WorkspaceServiceAccount
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "服务账号名称"
// @Success 200 {object} rbacModels.DeleteServiceAccountResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/serviceaccounts/{name} [delete]
func (r *WorkspaceRouter) DeleteWorkspaceServiceAccount(c *gin.Context) {
	req := rbacModels.DeleteServiceAccountRequest{
		ClusterID: c.Param("clusterId"),
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.DeleteServiceAccount(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ValidateWorkspaceServiceAccountName 校验工作空间服务账号名称
// @Summary 校验工作空间服务账号名称
// @Description 校验工作空间服务账号名称在指定命名空间内是否可用
// @Tags RBAC-WorkspaceServiceAccount
// @Accept json
// @Produce json
// @Param organizationId path string true "组织ID"
// @Param projectId path string true "项目ID"
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param body body rbacModels.ValidateServiceAccountNameRequest true "校验请求"
// @Success 200 {object} rbacModels.ValidateServiceAccountNameResponse
// @Router /apis/v1/organizations/{organizationId}/projects/{projectId}/clusters/{clusterId}/namespaces/{namespace}/serviceaccounts/validate [post]
func (r *WorkspaceRouter) ValidateWorkspaceServiceAccountName(c *gin.Context) {
	var req rbacModels.ValidateServiceAccountNameRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	// 从路径参数获取集群ID和命名空间
	req.ClusterID = c.Param("clusterId")
	req.Namespace = c.Param("namespace")

	resp, err := r.handler.ValidateServiceAccountName(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}
