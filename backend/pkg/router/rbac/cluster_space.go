package rbac

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/rbac"
	rbacModels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

// ClusterSpaceRouter 集群空间RBAC路由
type ClusterSpaceRouter struct {
	handler rbac.Handler
}

// NewClusterSpaceRouter 创建集群空间RBAC路由
func NewClusterSpaceRouter() *ClusterSpaceRouter {
	return &ClusterSpaceRouter{
		handler: rbac.NewHandler(),
	}
}

// RegisterRoutes 注册集群空间RBAC路由
func (r *ClusterSpaceRouter) RegisterRoutes(rg *gin.RouterGroup) {
	// 集群级别的Role列表查询（可通过namespace参数过滤）
	rg.GET("/roles", r.ListRole)

	// 命名空间级别的操作
	namespaceGroup := rg.Group("/namespaces/:namespace")
	{
		// Role相关路由
		roleGroup := namespaceGroup.Group("/roles")
		{
			roleGroup.POST("", r.CreateRole)
			roleGroup.GET("/:name", r.GetRole)
			roleGroup.PUT("/:name", r.UpdateRole)
			roleGroup.DELETE("/:name", r.DeleteRole)
		}

		// Role名称校验
		namespaceGroup.GET("/roles/validate", r.ValidateRoleName)

		// RoleBinding相关路由
		roleBindingGroup := namespaceGroup.Group("/rolebindings")
		{
			roleBindingGroup.POST("", r.CreateRoleBinding)
			roleBindingGroup.GET("/:name", r.GetRoleBinding)
			roleBindingGroup.GET("", r.ListRoleBinding)
			roleBindingGroup.PUT("/:name", r.UpdateRoleBinding)
			roleBindingGroup.DELETE("/:name", r.DeleteRoleBinding)
		}

		// ServiceAccount相关路由
		serviceAccountGroup := namespaceGroup.Group("/serviceaccounts")
		{
			serviceAccountGroup.POST("", r.CreateServiceAccount)
			serviceAccountGroup.GET("/:name", r.GetServiceAccount)
			serviceAccountGroup.PUT("/:name", r.UpdateServiceAccount)
			serviceAccountGroup.DELETE("/:name", r.DeleteServiceAccount)
		}

		// ServiceAccount名称校验
		namespaceGroup.POST("/serviceaccounts/validate", r.ValidateServiceAccountName)
	}

	// ClusterRole相关路由
	clusterRoleGroup := rg.Group("/clusterroles")
	{
		clusterRoleGroup.POST("", r.CreateClusterRole)
		clusterRoleGroup.GET("/:name", r.GetClusterRole)
		clusterRoleGroup.GET("", r.ListClusterRole)
		clusterRoleGroup.PUT("/:name", r.UpdateClusterRole)
		clusterRoleGroup.DELETE("/:name", r.DeleteClusterRole)
	}

	// ClusterRole名称校验
	rg.GET("/clusterroles/validate", r.ValidateClusterRoleName)

	// ClusterRoleBinding相关路由
	clusterRoleBindingGroup := rg.Group("/clusterrolebindings")
	{
		clusterRoleBindingGroup.POST("", r.CreateClusterRoleBinding)
		clusterRoleBindingGroup.GET("/:name", r.GetClusterRoleBinding)
		clusterRoleBindingGroup.GET("", r.ListClusterRoleBinding)
		clusterRoleBindingGroup.PUT("/:name", r.UpdateClusterRoleBinding)
		clusterRoleBindingGroup.DELETE("/:name", r.DeleteClusterRoleBinding)
	}

	// ClusterRoleBinding名称校验
	rg.GET("/clusterrolebindings/validate", r.ValidateClusterRoleBindingName)

	// ServiceAccount列表查询（集群级别）
	rg.GET("/serviceaccounts", r.ListServiceAccount)

	// API资源发现路由
	rg.GET("/resources", r.GetAPIResources)
}

// ===== Role 相关路由处理器 =====

// CreateRole 创建角色
// @Summary 创建角色
// @Description 在指定集群和命名空间中创建角色
// @Tags RBAC-Role
// @Accept json
// @Produce json
// @Param cluster_id path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param body body rbacModels.CreateRoleRequest true "创建角色请求"
// @Success 200 {object} rbacModels.CreateRoleResponse
// @Failure 400 {object} router.ErrorResponse
// @Failure 409 {object} router.ErrorResponse "角色已存在"
// @Router /apis/v1/clusters/{cluster_id}/namespaces/{namespace}/roles [post]
func (r *ClusterSpaceRouter) CreateRole(c *gin.Context) {
	var req rbacModels.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	// 从路径参数获取集群ID和命名空间
	req.ClusterID = c.Param("clusterId")
	req.Namespace = c.Param("namespace")

	resp, err := r.handler.CreateRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// GetRole 获取角色详情
// @Summary 获取角色详情
// @Description 获取指定角色的详细信息
// @Tags RBAC-Role
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "角色名称"
// @Success 200 {object} rbacModels.GetRoleResponse
// @Failure 404 {object} router.ErrorResponse "角色不存在"
// @Router /api/v1/clusters/{clusterId}/namespaces/{namespace}/rbac/roles/{name} [get]
func (r *ClusterSpaceRouter) GetRole(c *gin.Context) {
	req := rbacModels.GetRoleRequest{
		ClusterID: c.Param("clusterId"),
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.GetRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ListRole 获取角色列表
// @Summary 获取角色列表
// @Description 获取指定集群的角色列表，可通过namespace参数过滤
// @Tags RBAC-Role
// @Accept json
// @Produce json
// @Param clusterId path string true "集群ID"
// @Param namespace query string false "命名空间过滤"
// @Success 200 {object} rbacModels.ListRoleResponse
// @Router /apis/v1/clusters/{clusterId}/roles [get]
func (r *ClusterSpaceRouter) ListRole(c *gin.Context) {
	req := rbacModels.ListRoleRequest{
		ClusterID: c.Param("clusterId"),
		Namespace: c.Query("namespace"),
	}

	// 设置过滤器
	req.Filter = utils.ParseQueryParams[*rbacModels.Role](c)

	resp, err := r.handler.ListRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// UpdateRole 更新角色
// @Summary 更新角色
// @Description 更新指定角色的信息
// @Tags RBAC-Role
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "角色名称"
// @Param body body rbacModels.Role true "角色信息"
// @Success 200 {object} rbacModels.UpdateRoleResponse
// @Failure 404 {object} router.ErrorResponse "角色不存在"
// @Router /api/v1/clusters/{clusterId}/namespaces/{namespace}/rbac/roles/{name} [put]
func (r *ClusterSpaceRouter) UpdateRole(c *gin.Context) {
	var role rbacModels.Role
	if err := c.ShouldBindJSON(&role); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	req := rbacModels.UpdateRoleRequest{
		ClusterID: c.Param("clusterId"),
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
		Role:      &role,
	}

	resp, err := r.handler.UpdateRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// DeleteRole 删除角色
// @Summary 删除角色
// @Description 删除指定的角色
// @Tags RBAC-Role
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param namespace query string true "命名空间"
// @Param name path string true "角色名称"
// @Success 200 {object} rbacModels.DeleteRoleResponse
// @Failure 404 {object} router.ErrorResponse "角色不存在"
// @Failure 409 {object} router.ErrorResponse "角色正在被使用"
// @Router /api/v1/clusters/{clusterId}/namespaces/{namespace}/rbac/roles/{name} [delete]
func (r *ClusterSpaceRouter) DeleteRole(c *gin.Context) {
	req := rbacModels.DeleteRoleRequest{
		ClusterID: c.Param("clusterId"),
		Namespace: c.Param("namespace"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.DeleteRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ValidateRoleName 校验角色名称
// @Summary 校验角色名称
// @Description 校验角色名称在指定命名空间内是否可用
// @Tags RBAC-Role
// @Accept json
// @Produce json
// @Param clusterId path string true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name query string true "需要校验的角色名称"
// @Success 200 {object} rbacModels.ValidateRoleNameResponse
// @Failure 409 {object} router.ErrorResponse "角色名称已存在"
// @Router /apis/v1/clusters/{clusterId}/namespaces/{namespace}/roles/validate [get]
func (r *ClusterSpaceRouter) ValidateRoleName(c *gin.Context) {
	req := rbacModels.ValidateRoleNameRequest{
		ClusterID: c.Param("clusterId"),
		Namespace: c.Param("namespace"),
		Name:      c.Query("name"),
	}

	resp, err := r.handler.ValidateRoleName(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ===== ClusterRole 相关路由处理器 =====

// CreateClusterRole 创建集群角色
// @Summary 创建集群角色
// @Description 在指定集群中创建集群角色
// @Tags RBAC-ClusterRole
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param body body rbacModels.CreateClusterRoleRequest true "创建集群角色请求"
// @Success 200 {object} rbacModels.CreateClusterRoleResponse
// @Failure 400 {object} router.ErrorResponse
// @Failure 409 {object} router.ErrorResponse "集群角色已存在"
// @Router /api/v1/clusters/{clusterId}/rbac/cluster-roles [post]
func (r *ClusterSpaceRouter) CreateClusterRole(c *gin.Context) {
	var req rbacModels.CreateClusterRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	// 从路径参数获取集群ID
	req.ClusterID = c.Param("clusterId")

	resp, err := r.handler.CreateClusterRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// GetClusterRole 获取集群角色详情
// @Summary 获取集群角色详情
// @Description 获取指定集群角色的详细信息
// @Tags RBAC-ClusterRole
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param name path string true "集群角色名称"
// @Success 200 {object} rbacModels.GetClusterRoleResponse
// @Failure 404 {object} router.ErrorResponse "集群角色不存在"
// @Router /api/v1/clusters/{clusterId}/rbac/cluster-roles/{name} [get]
func (r *ClusterSpaceRouter) GetClusterRole(c *gin.Context) {
	req := rbacModels.GetClusterRoleRequest{
		ClusterID: c.Param("clusterId"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.GetClusterRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ListClusterRole 获取集群角色列表
// @Summary 获取集群角色列表
// @Description 获取指定集群的集群角色列表
// @Tags RBAC-ClusterRole
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Success 200 {object} rbacModels.ListClusterRoleResponse
// @Router /api/v1/clusters/{clusterId}/rbac/cluster-roles [get]
func (r *ClusterSpaceRouter) ListClusterRole(c *gin.Context) {
	req := rbacModels.ListClusterRoleRequest{
		ClusterID: c.Param("clusterId"),
	}

	// 设置过滤器
	req.Filter = utils.ParseQueryParams[*rbacModels.ClusterRole](c)

	resp, err := r.handler.ListClusterRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// UpdateClusterRole 更新集群角色
// @Summary 更新集群角色
// @Description 更新指定集群角色的信息
// @Tags RBAC-ClusterRole
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param name path string true "集群角色名称"
// @Param body body rbacModels.ClusterRole true "集群角色信息"
// @Success 200 {object} rbacModels.UpdateClusterRoleResponse
// @Failure 404 {object} router.ErrorResponse "集群角色不存在"
// @Router /api/v1/clusters/{clusterId}/rbac/cluster-roles/{name} [put]
func (r *ClusterSpaceRouter) UpdateClusterRole(c *gin.Context) {
	var clusterRole rbacModels.ClusterRole
	if err := c.ShouldBindJSON(&clusterRole); err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	req := rbacModels.UpdateClusterRoleRequest{
		ClusterID:   c.Param("clusterId"),
		Name:        c.Param("name"),
		ClusterRole: &clusterRole,
	}

	resp, err := r.handler.UpdateClusterRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// DeleteClusterRole 删除集群角色
// @Summary 删除集群角色
// @Description 删除指定的集群角色
// @Tags RBAC-ClusterRole
// @Accept json
// @Produce json
// @Param clusterId query string true "集群ID"
// @Param name path string true "集群角色名称"
// @Success 200 {object} rbacModels.DeleteClusterRoleResponse
// @Failure 404 {object} router.ErrorResponse "集群角色不存在"
// @Failure 409 {object} router.ErrorResponse "集群角色正在被使用"
// @Router /api/v1/clusters/{clusterId}/rbac/cluster-roles/{name} [delete]
func (r *ClusterSpaceRouter) DeleteClusterRole(c *gin.Context) {
	req := rbacModels.DeleteClusterRoleRequest{
		ClusterID: c.Param("clusterId"),
		Name:      c.Param("name"),
	}

	resp, err := r.handler.DeleteClusterRole(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}

// ValidateClusterRoleName 校验集群角色名称
// @Summary 校验集群角色名称
// @Description 校验集群角色名称是否可用
// @Tags RBAC-ClusterRole
// @Accept json
// @Produce json
// @Param clusterId path string true "集群ID"
// @Param name query string true "需要校验的集群角色名称"
// @Success 200 {object} rbacModels.ValidateClusterRoleNameResponse
// @Failure 409 {object} router.ErrorResponse "集群角色名称已存在"
// @Router /apis/v1/clusters/{clusterId}/clusterroles/validate [get]
func (r *ClusterSpaceRouter) ValidateClusterRoleName(c *gin.Context) {
	req := rbacModels.ValidateClusterRoleNameRequest{
		ClusterID: c.Param("clusterId"),
		Name:      c.Query("name"),
	}

	resp, err := r.handler.ValidateClusterRoleName(c.Request.Context(), &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c.Request.Context(), err))
		return
	}

	utils.Succeed(c, resp)
}
