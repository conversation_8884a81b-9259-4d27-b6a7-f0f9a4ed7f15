package rbac

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	rbacv1 "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// CreateRole 创建角色
func (h *handler) CreateRole(ctx context.Context, req *rbac.CreateRoleRequest) (resp *rbac.CreateRoleResponse, err error) {
	if l := logger.GetLogger(); l != nil { l.Info("Creating role",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	// 验证策略规则
	if err := validatePolicyRules(req.Rules); err != nil {
		return nil, err
	}

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 构建Role对象
	role := &rbacv1.Role{
		TypeMeta: metav1.TypeMeta{
			APIVersion: constants.RBACAPIGroup + "/" + constants.RBACAPIVersion,
			Kind:       constants.RoleKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      req.Name,
			Namespace: req.Namespace,
			Labels:    req.Labels,
		},
		Rules: req.Rules,
	}

	// 创建Role
	if err := k8sClient.Create(ctx, role); err != nil {
		if apierrors.IsAlreadyExists(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACRoleAlreadyExists, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	if l := logger.GetLogger(); l != nil { l.Info("Role created successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	return &rbac.CreateRoleResponse{
		ObjectMeta: role.ObjectMeta,
		Rules:      role.Rules,
	}, nil
}

// GetRole 获取角色详情
func (h *handler) GetRole(ctx context.Context, req *rbac.GetRoleRequest) (resp *rbac.GetRoleResponse, err error) {
	if l := logger.GetLogger(); l != nil { l.Info("Getting role",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取Role
	role := &rbacv1.Role{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, role); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACRoleNotFound, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	return &rbac.Role{
		TypeMeta:   role.TypeMeta,
		ObjectMeta: role.ObjectMeta,
		Rules:      role.Rules,
	}, nil
}

// ListRole 获取角色列表
func (h *handler) ListRole(ctx context.Context, req *rbac.ListRoleRequest) (resp *rbac.ListRoleResponse, err error) {
	if l := logger.GetLogger(); l != nil { l.Info("Listing roles",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取Role列表
	roleList := &rbacv1.RoleList{}
	listOptions := []client.ListOption{}

	// 如果指定了命名空间，则只查询该命名空间
	if req.Namespace != "" {
		listOptions = append(listOptions, client.InNamespace(req.Namespace))
	}

	if err := k8sClient.List(ctx, roleList, listOptions...); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	// 转换为业务模型
	var items []*rbac.Role
	for _, role := range roleList.Items {
		items = append(items, &rbac.Role{
			TypeMeta:   role.TypeMeta,
			ObjectMeta: role.ObjectMeta,
			Rules:      role.Rules,
		})
	}

	// 使用Filter过滤器过滤角色列表
	result, err := req.Filter.FilterResult(items)
	if err != nil {
		return nil, err
	}

	if l := logger.GetLogger(); l != nil { l.Info("Roles listed successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.Int("total", result.TotalCount),
	)

	return result, nil
}

// UpdateRole 更新角色
func (h *handler) UpdateRole(ctx context.Context, req *rbac.UpdateRoleRequest) (resp *rbac.UpdateRoleResponse, err error) {
	if l := logger.GetLogger(); l != nil { l.Info("Updating role",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	// 验证策略规则
	if err := validatePolicyRules(req.Role.Rules); err != nil {
		return nil, err
	}

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取现有Role
	existingRole := &rbacv1.Role{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, existingRole); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACRoleNotFound, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 更新Role
	existingRole.Rules = req.Role.Rules
	if req.Role.Labels != nil {
		existingRole.Labels = req.Role.Labels
	}
	if req.Role.Annotations != nil {
		existingRole.Annotations = req.Role.Annotations
	}

	if err := k8sClient.Update(ctx, existingRole); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	if l := logger.GetLogger(); l != nil { l.Info("Role updated successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	return &rbac.UpdateRoleResponse{
		Name: req.Name,
	}, nil
}

// DeleteRole 删除角色
func (h *handler) DeleteRole(ctx context.Context, req *rbac.DeleteRoleRequest) (resp *rbac.DeleteRoleResponse, err error) {
	if l := logger.GetLogger(); l != nil { l.Info("Deleting role",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 检查Role是否被使用
	if err := checkRoleInUse(ctx, k8sClient, req.Namespace, req.Name); err != nil {
		return nil, err
	}

	// 获取Role
	role := &rbacv1.Role{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, role); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACRoleNotFound, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 删除Role
	if err := k8sClient.Delete(ctx, role); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	if l := logger.GetLogger(); l != nil { l.Info("Role deleted successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	return &rbac.DeleteRoleResponse{
		Message: fmt.Sprintf("Role '%s' deleted successfully", req.Name),
	}, nil
}

// ValidateRoleName 校验角色名称
func (h *handler) ValidateRoleName(ctx context.Context, req *rbac.ValidateRoleNameRequest) (resp *rbac.ValidateRoleNameResponse, err error) {
	if l := logger.GetLogger(); l != nil { l.Info("Validating role name",
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 检查Role是否存在
	role := &rbacv1.Role{}
	err = k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, role)

	if err != nil {
		if apierrors.IsNotFound(err) {
			// 名称可用
			return &rbac.ValidateRoleNameResponse{
				Message: fmt.Sprintf("Role name '%s' is available", req.Name),
			}, nil
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 名称已存在
	return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACRoleAlreadyExists, req.Name, req.Namespace)
}
