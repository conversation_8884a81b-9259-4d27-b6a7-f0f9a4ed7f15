package rbac

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	rbac "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	rbacv1 "k8s.io/api/rbac/v1"
)

func TestListWorkspaceRole(t *testing.T) {
	h := &handler{}

	req := &rbac.WorkspaceListRoleRequest{
		WorkspaceRoleRequest: rbac.WorkspaceRoleRequest{
			OrganizationID: "org1",
			ProjectID:      "proj1",
			ClusterID:      "test-cluster",
		},
		Namespace: "test-namespace",
	}

	// 由于没有真实的K8s集群，这个测试会失败，但我们可以验证参数转换
	resp, err := h.ListWorkspaceRole(context.Background(), req)
	// 预期会有错误，因为没有真实的集群连接
	assert.Error(t, err)
	assert.Nil(t, resp)
}

func TestGetWorkspaceRole(t *testing.T) {
	h := &handler{}

	req := &rbac.WorkspaceGetRoleRequest{
		WorkspaceRoleRequest: rbac.WorkspaceRoleRequest{
			OrganizationID: "org1",
			ProjectID:      "proj1",
			ClusterID:      "test-cluster",
		},
		Namespace: "test-namespace",
		Name:      "test-role",
	}

	// 由于没有真实的K8s集群，这个测试会失败，但我们可以验证参数转换
	resp, err := h.GetWorkspaceRole(context.Background(), req)
	// 预期会有错误，因为没有真实的集群连接
	assert.Error(t, err)
	assert.Nil(t, resp)
}

func TestCreateWorkspaceRole(t *testing.T) {
	h := &handler{}

	req := &rbac.WorkspaceCreateRoleRequest{
		WorkspaceRoleRequest: rbac.WorkspaceRoleRequest{
			OrganizationID: "org1",
			ProjectID:      "proj1",
			ClusterID:      "test-cluster",
		},
		Namespace: "test-namespace",
		Name:      "test-role",
		Labels: map[string]string{
			"test": "label",
		},
		Rules: []rbacv1.PolicyRule{
			{
				Verbs:     []string{"get", "list"},
				Resources: []string{"pods"},
			},
		},
	}

	// 由于没有真实的K8s集群，这个测试会失败，但我们可以验证参数转换
	resp, err := h.CreateWorkspaceRole(context.Background(), req)
	// 预期会有错误，因为没有真实的集群连接
	assert.Error(t, err)
	assert.Nil(t, resp)
}

func TestUpdateWorkspaceRole(t *testing.T) {
	h := &handler{}

	req := &rbac.WorkspaceUpdateRoleRequest{
		WorkspaceRoleRequest: rbac.WorkspaceRoleRequest{
			OrganizationID: "org1",
			ProjectID:      "proj1",
			ClusterID:      "test-cluster",
		},
		Namespace: "test-namespace",
		Name:      "test-role",
		Role: &rbac.Role{
			Rules: []rbacv1.PolicyRule{
				{
					Verbs:     []string{"get", "list", "create"},
					Resources: []string{"pods"},
				},
			},
		},
	}

	// 由于没有真实的K8s集群，这个测试会失败，但我们可以验证参数转换
	resp, err := h.UpdateWorkspaceRole(context.Background(), req)
	// 预期会有错误，因为没有真实的集群连接
	assert.Error(t, err)
	assert.Nil(t, resp)
}

func TestDeleteWorkspaceRole(t *testing.T) {
	h := &handler{}

	req := &rbac.WorkspaceDeleteRoleRequest{
		WorkspaceRoleRequest: rbac.WorkspaceRoleRequest{
			OrganizationID: "org1",
			ProjectID:      "proj1",
			ClusterID:      "test-cluster",
		},
		Namespace: "test-namespace",
		Name:      "test-role",
	}

	// 由于没有真实的K8s集群，这个测试会失败，但我们可以验证参数转换
	resp, err := h.DeleteWorkspaceRole(context.Background(), req)
	// 预期会有错误，因为没有真实的集群连接
	assert.Error(t, err)
	assert.Nil(t, resp)
}

func TestGetBindableRoles(t *testing.T) {
	h := &handler{}

	req := &rbac.GetBindableRolesRequest{
		OrganizationID: "org1",
		ProjectID:      "proj1",
		ClusterID:      "test-cluster",
		Namespace:      "test-namespace",
	}

	// 由于没有真实的K8s集群，这个测试会失败，但我们可以验证参数转换
	resp, err := h.GetBindableRoles(context.Background(), req)
	// 预期会有错误，因为没有真实的集群连接
	assert.Error(t, err)
	assert.Nil(t, resp)
}
