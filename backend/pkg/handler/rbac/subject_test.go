package rbac

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	rbac "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
)

func TestGetSubjects(t *testing.T) {
	h := &handler{}

	tests := []struct {
		name     string
		req      *rbac.GetSubjectsRequest
		wantErr  bool
		validate func(t *testing.T, resp *rbac.GetSubjectsResponse)
	}{
		{
			name: "Get all subjects",
			req: &rbac.GetSubjectsRequest{
				Type: "",
			},
			wantErr: false,
			validate: func(t *testing.T, resp *rbac.GetSubjectsResponse) {
				assert.NotNil(t, resp)
				assert.NotEmpty(t, resp.Subjects)
				// 应该包含所有类型的主体
				hasUser := false
				hasRole := false
				hasTenant := false
				hasProject := false
				for _, subject := range resp.Subjects {
					switch subject.Kind {
					case rbac.SubjectKindPlatformUser:
						hasUser = true
					case rbac.SubjectKindPlatformRole:
						hasRole = true
					case rbac.SubjectKindTenant:
						hasTenant = true
					case rbac.SubjectKindProject:
						hasProject = true
					}
				}
				assert.True(t, hasUser, "Should contain platform users")
				assert.True(t, hasRole, "Should contain platform roles")
				assert.True(t, hasTenant, "Should contain tenants")
				assert.True(t, hasProject, "Should contain projects")
			},
		},
		{
			name: "Get platform users only",
			req: &rbac.GetSubjectsRequest{
				Type: string(rbac.SubjectKindPlatformUser),
			},
			wantErr: false,
			validate: func(t *testing.T, resp *rbac.GetSubjectsResponse) {
				assert.NotNil(t, resp)
				assert.NotEmpty(t, resp.Subjects)
				// 应该只包含平台用户
				for _, subject := range resp.Subjects {
					assert.Equal(t, rbac.SubjectKindPlatformUser, subject.Kind)
				}
			},
		},
		{
			name: "Get platform roles only",
			req: &rbac.GetSubjectsRequest{
				Type: string(rbac.SubjectKindPlatformRole),
			},
			wantErr: false,
			validate: func(t *testing.T, resp *rbac.GetSubjectsResponse) {
				assert.NotNil(t, resp)
				assert.NotEmpty(t, resp.Subjects)
				// 应该只包含平台角色
				for _, subject := range resp.Subjects {
					assert.Equal(t, rbac.SubjectKindPlatformRole, subject.Kind)
				}
			},
		},
		{
			name: "Get tenants only",
			req: &rbac.GetSubjectsRequest{
				Type: string(rbac.SubjectKindTenant),
			},
			wantErr: false,
			validate: func(t *testing.T, resp *rbac.GetSubjectsResponse) {
				assert.NotNil(t, resp)
				assert.NotEmpty(t, resp.Subjects)
				// 应该只包含租户
				for _, subject := range resp.Subjects {
					assert.Equal(t, rbac.SubjectKindTenant, subject.Kind)
				}
			},
		},
		{
			name: "Get projects only",
			req: &rbac.GetSubjectsRequest{
				Type: string(rbac.SubjectKindProject),
			},
			wantErr: false,
			validate: func(t *testing.T, resp *rbac.GetSubjectsResponse) {
				assert.NotNil(t, resp)
				assert.NotEmpty(t, resp.Subjects)
				// 应该只包含项目
				for _, subject := range resp.Subjects {
					assert.Equal(t, rbac.SubjectKindProject, subject.Kind)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := h.GetSubjects(context.Background(), tt.req)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				if tt.validate != nil {
					tt.validate(t, resp)
				}
			}
		})
	}
}
