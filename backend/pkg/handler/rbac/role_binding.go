package rbac

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	rbacv1 "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// CreateRoleBinding 创建角色绑定
func (h *handler) CreateRoleBinding(ctx context.Context, req *rbac.CreateRoleBindingRequest) (resp *rbac.CreateRoleBindingResponse, err error) {
	if l := logger.GetLogger(); l != nil {
		l.Info("Creating role binding",
			zap.String("cluster", req.ClusterID),
			zap.String("namespace", req.Namespace),
			zap.String("roleBindingName", req.Name),
			zap.String("roleName", req.RoleName),
			zap.String("roleKind", req.RoleKind),
		)
	}

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 转换业务Subject为Kubernetes Subject
	k8sSubjects, err := convertBusinessSubjectsToK8s(req.Subjects)
	if err != nil {
		return nil, err
	}

	// 构建RoleBinding对象
	roleBinding := &rbacv1.RoleBinding{
		TypeMeta: metav1.TypeMeta{
			APIVersion: constants.RBACAPIGroup + "/" + constants.RBACAPIVersion,
			Kind:       constants.RoleBindingKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      req.Name,
			Namespace: req.Namespace,
		},
		Subjects: k8sSubjects,
		RoleRef:  buildRoleRef(req.RoleName, req.RoleKind),
	}

	// 创建RoleBinding
	if err := k8sClient.Create(ctx, roleBinding); err != nil {
		if apierrors.IsAlreadyExists(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACRoleBindingAlreadyExists, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	if l := logger.GetLogger(); l != nil {
		l.Info("RoleBinding created successfully",
			zap.String("cluster", req.ClusterID),
			zap.String("namespace", req.Namespace),
			zap.String("roleBindingName", req.Name),
		)
	}

	return &rbac.CreateRoleBindingResponse{
		Name: req.Name,
	}, nil
}

// GetRoleBinding 获取角色绑定详情
func (h *handler) GetRoleBinding(ctx context.Context, req *rbac.GetRoleBindingRequest) (resp *rbac.GetRoleBindingResponse, err error) {
	if l := logger.GetLogger(); l != nil {
		l.Info("Getting role binding",
			zap.String("cluster", req.ClusterID),
			zap.String("namespace", req.Namespace),
			zap.String("roleBindingName", req.Name),
		)
	}

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取RoleBinding
	roleBinding := &rbacv1.RoleBinding{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, roleBinding); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACRoleBindingNotFound, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 转换Kubernetes Subject为业务Subject
	businessSubjects := convertK8sSubjectsToBusiness(roleBinding.Subjects)

	return &rbac.RoleBinding{
		TypeMeta:   roleBinding.TypeMeta,
		ObjectMeta: roleBinding.ObjectMeta,
		Subjects:   businessSubjects,
		RoleRef:    roleBinding.RoleRef,
	}, nil
}

// ListRoleBinding 获取角色绑定列表
func (h *handler) ListRoleBinding(ctx context.Context, req *rbac.ListRoleBindingRequest) (resp *rbac.ListRoleBindingResponse, err error) {
	if l := logger.GetLogger(); l != nil {
		l.Info("Listing role bindings",
			zap.String("cluster", req.ClusterID),
			zap.String("namespace", req.Namespace),
		)
	}

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取RoleBinding列表
	roleBindingList := &rbacv1.RoleBindingList{}
	if err := k8sClient.List(ctx, roleBindingList, client.InNamespace(req.Namespace)); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	// 转换为业务模型
	var items []*rbac.RoleBinding
	for _, rb := range roleBindingList.Items {
		businessSubjects := convertK8sSubjectsToBusiness(rb.Subjects)
		items = append(items, &rbac.RoleBinding{
			TypeMeta:   rb.TypeMeta,
			ObjectMeta: rb.ObjectMeta,
			Subjects:   businessSubjects,
			RoleRef:    rb.RoleRef,
		})
	}

	// 使用Filter过滤器过滤角色绑定列表
	result, err := req.Filter.FilterResult(items)
	if err != nil {
		return nil, err
	}

	if l := logger.GetLogger(); l != nil {
		l.Info("RoleBindings listed successfully",
			zap.String("cluster", req.ClusterID),
			zap.String("namespace", req.Namespace),
			zap.Int("total", result.TotalCount),
		)
	}

	return result, nil
}

// UpdateRoleBinding 更新角色绑定
func (h *handler) UpdateRoleBinding(ctx context.Context, req *rbac.UpdateRoleBindingRequest) (resp *rbac.UpdateRoleBindingResponse, err error) {
	if l := logger.GetLogger(); l != nil {
		l.Info("Updating role binding",
			zap.String("cluster", req.ClusterID),
			zap.String("namespace", req.Namespace),
			zap.String("roleBindingName", req.Name),
		)
	}

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 转换业务Subject为Kubernetes Subject
	k8sSubjects, err := convertBusinessSubjectsToK8s(req.Subjects)
	if err != nil {
		return nil, err
	}

	// 获取现有RoleBinding
	existingRoleBinding := &rbacv1.RoleBinding{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, existingRoleBinding); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACRoleBindingNotFound, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 更新RoleBinding
	existingRoleBinding.Subjects = k8sSubjects
	existingRoleBinding.RoleRef = buildRoleRef(req.RoleName, req.RoleKind)

	if err := k8sClient.Update(ctx, existingRoleBinding); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	if l := logger.GetLogger(); l != nil {
		l.Info("RoleBinding updated successfully",
			zap.String("cluster", req.ClusterID),
			zap.String("namespace", req.Namespace),
			zap.String("roleBindingName", req.Name),
		)
	}

	return &rbac.UpdateRoleBindingResponse{
		Name: req.Name,
	}, nil
}

// DeleteRoleBinding 删除角色绑定
func (h *handler) DeleteRoleBinding(ctx context.Context, req *rbac.DeleteRoleBindingRequest) (resp *rbac.DeleteRoleBindingResponse, err error) {
	if l := logger.GetLogger(); l != nil {
		l.Info("Deleting role binding",
			zap.String("cluster", req.ClusterID),
			zap.String("namespace", req.Namespace),
			zap.String("roleBindingName", req.Name),
		)
	}

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取RoleBinding
	roleBinding := &rbacv1.RoleBinding{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, roleBinding); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACRoleBindingNotFound, req.Name, req.Namespace)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 删除RoleBinding
	if err := k8sClient.Delete(ctx, roleBinding); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	if l := logger.GetLogger(); l != nil {
		l.Info("RoleBinding deleted successfully",
			zap.String("cluster", req.ClusterID),
			zap.String("namespace", req.Namespace),
			zap.String("roleBindingName", req.Name),
		)
	}

	return &rbac.DeleteRoleBindingResponse{
		Message: fmt.Sprintf("RoleBinding '%s' deleted successfully", req.Name),
	}, nil
}

// ValidateRoleBindingName 校验角色绑定名称
func (h *handler) ValidateRoleBindingName(ctx context.Context, req *rbac.ValidateRoleBindingNameRequest) (resp *rbac.ValidateRoleBindingNameResponse, err error) {
	if l := logger.GetLogger(); l != nil {
		l.Info("Validating role binding name",
			zap.String("cluster", req.ClusterID),
			zap.String("namespace", req.Namespace),
			zap.String("name", req.Name),
		)
	}

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 检查角色绑定是否已存在
	roleBinding := &rbacv1.RoleBinding{}
	err = k8sClient.Get(ctx, client.ObjectKey{
		Namespace: req.Namespace,
		Name:      req.Name,
	}, roleBinding)

	if err != nil {
		if client.IgnoreNotFound(err) == nil {
			// 角色绑定不存在，名称可用
			resp = &rbac.ValidateRoleBindingNameResponse{
				Available: true,
				Message:   "角色绑定名称可用",
			}
		} else {
			// 其他错误
			return nil, errors.NewFromError(ctx, err)
		}
	} else {
		// 角色绑定已存在
		resp = &rbac.ValidateRoleBindingNameResponse{
			Available: false,
			Message:   "角色绑定名称已存在",
		}
	}

	return resp, nil
}
