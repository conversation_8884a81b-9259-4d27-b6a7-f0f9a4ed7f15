package rbac

import (
	"context"
	"fmt"
	"strings"

	clusterClient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"

	"k8s.io/apimachinery/pkg/runtime/schema"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// getClusterClient 获取集群客户端
func getClusterClient(clusterID string) (client.Client, error) {
	cluster, err := clusterClient.GetCluster(clusterID)
	if err != nil {
		return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterNotFound, clusterID)
	}
	return cluster.GetClient().GetCtrlClient(), nil
}

// convertBusinessSubjectsToK8s 将业务Subject转换为Kubernetes Subject
func convertBusinessSubjectsToK8s(businessSubjects []rbac.Subject) ([]rbacv1.Subject, error) {
	k8sSubjects := make([]rbacv1.Subject, 0, len(businessSubjects))

	for _, bs := range businessSubjects {
		k8sSubject, err := convertBusinessSubjectToK8s(bs)
		if err != nil {
			return nil, err
		}
		k8sSubjects = append(k8sSubjects, k8sSubject)
	}

	return k8sSubjects, nil
}

// convertBusinessSubjectToK8s 将单个业务Subject转换为Kubernetes Subject
func convertBusinessSubjectToK8s(bs rbac.Subject) (rbacv1.Subject, error) {
	switch bs.Kind {
	case rbac.SubjectKindPlatformUser, rbac.SubjectKindPlatformRole:
		// 平台用户和角色映射为Kubernetes User
		return rbacv1.Subject{
			Kind:     constants.UserSubjectKind,
			Name:     fmt.Sprintf(constants.PlatformEntityNameFormat, constants.PlatformRBACPrefix, string(bs.Kind), bs.ID),
			APIGroup: constants.RBACAPIGroup,
		}, nil

	case rbac.SubjectKindTenant, rbac.SubjectKindProject:
		// 租户和项目映射为Kubernetes Group
		return rbacv1.Subject{
			Kind:     constants.GroupSubjectKind,
			Name:     fmt.Sprintf(constants.PlatformEntityNameFormat, constants.PlatformRBACPrefix, string(bs.Kind), bs.ID),
			APIGroup: constants.RBACAPIGroup,
		}, nil

	case rbac.SubjectKindServiceAccount:
		// ServiceAccount直接映射
		return rbacv1.Subject{
			Kind:      constants.ServiceAccountSubjectKind,
			Name:      bs.Name,
			Namespace: bs.Namespace,
		}, nil

	case rbac.SubjectKindKubernetesUser:
		// Kubernetes原生User
		return rbacv1.Subject{
			Kind:     constants.UserSubjectKind,
			Name:     bs.Name,
			APIGroup: constants.RBACAPIGroup,
		}, nil

	case rbac.SubjectKindKubernetesGroup:
		// Kubernetes原生Group
		return rbacv1.Subject{
			Kind:     constants.GroupSubjectKind,
			Name:     bs.Name,
			APIGroup: constants.RBACAPIGroup,
		}, nil

	default:
		return rbacv1.Subject{}, errors.NewFromCodeFormatMessage(errors.Var.RBACInvalidSubjectKind, string(bs.Kind))
	}
}

// convertK8sSubjectsToBusinesss 将Kubernetes Subject转换为业务Subject
func convertK8sSubjectsToBusiness(k8sSubjects []rbacv1.Subject) []rbac.Subject {
	businessSubjects := make([]rbac.Subject, 0, len(k8sSubjects))

	for _, ks := range k8sSubjects {
		bs := convertK8sSubjectToBusiness(ks)
		businessSubjects = append(businessSubjects, bs)
	}

	return businessSubjects
}

// convertK8sSubjectToBusiness 将单个Kubernetes Subject转换为业务Subject
func convertK8sSubjectToBusiness(ks rbacv1.Subject) rbac.Subject {
	switch ks.Kind {
	case constants.UserSubjectKind:
		// 检查是否为平台实体
		if strings.HasPrefix(ks.Name, constants.PlatformRBACPrefix+":") {
			parts := strings.Split(ks.Name, ":")
			if len(parts) == 3 {
				entityType := parts[1]
				entityID := parts[2]

				switch entityType {
				case constants.PlatformTypeAccount:
					return rbac.Subject{
						Kind: rbac.SubjectKindPlatformUser,
						ID:   entityID,
						Name: entityID, // 这里可以通过查询平台用户服务获取真实姓名
					}
				case constants.PlatformTypeRole:
					return rbac.Subject{
						Kind: rbac.SubjectKindPlatformRole,
						ID:   entityID,
						Name: entityID, // 这里可以通过查询平台角色服务获取真实名称
					}
				}
			}
		}
		// 原生Kubernetes User
		return rbac.Subject{
			Kind: rbac.SubjectKindKubernetesUser,
			Name: ks.Name,
		}

	case constants.GroupSubjectKind:
		// 检查是否为平台实体
		if strings.HasPrefix(ks.Name, constants.PlatformRBACPrefix+":") {
			parts := strings.Split(ks.Name, ":")
			if len(parts) == 3 {
				entityType := parts[1]
				entityID := parts[2]

				switch entityType {
				case constants.PlatformTypeTenant:
					return rbac.Subject{
						Kind: rbac.SubjectKindTenant,
						ID:   entityID,
						Name: entityID, // 这里可以通过查询平台租户服务获取真实名称
					}
				case constants.PlatformTypeProject:
					return rbac.Subject{
						Kind: rbac.SubjectKindProject,
						ID:   entityID,
						Name: entityID, // 这里可以通过查询平台项目服务获取真实名称
					}
				}
			}
		}
		// 原生Kubernetes Group
		return rbac.Subject{
			Kind: rbac.SubjectKindKubernetesGroup,
			Name: ks.Name,
		}

	case constants.ServiceAccountSubjectKind:
		return rbac.Subject{
			Kind:      rbac.SubjectKindServiceAccount,
			Name:      ks.Name,
			Namespace: ks.Namespace,
		}

	default:
		// 未知类型，返回原生User
		return rbac.Subject{
			Kind: rbac.SubjectKindKubernetesUser,
			Name: ks.Name,
		}
	}
}

// isSystemProtectedNamespace 检查是否为系统保护的命名空间
func isSystemProtectedNamespace(namespace string) bool {
	systemNamespaces := []string{
		constants.KubeSystemNamespace,
		constants.KubePublicNamespace,
		constants.KubeNodeLeaseNamespace,
	}

	for _, ns := range systemNamespaces {
		if namespace == ns {
			return true
		}
	}
	return false
}

// isDefaultServiceAccount 检查是否为默认ServiceAccount
func isDefaultServiceAccount(name string) bool {
	return name == constants.DefaultServiceAccountName
}

// hasSystemProtectedLabel 检查资源是否有系统保护标签
func hasSystemProtectedLabel(labels map[string]string) bool {
	if labels == nil {
		return false
	}
	return labels[constants.SystemProtectedLabel] == constants.SystemProtectedValue
}

// isServiceAccountProtected 检查ServiceAccount是否受保护
func isServiceAccountProtected(sa *corev1.ServiceAccount) bool {
	// 检查命名空间
	if isSystemProtectedNamespace(sa.Namespace) {
		return true
	}

	// 检查名称
	if isDefaultServiceAccount(sa.Name) {
		return true
	}

	// 检查保护标签
	if hasSystemProtectedLabel(sa.Labels) {
		return true
	}

	return false
}

// checkRoleInUse 检查Role是否被RoleBinding使用
func checkRoleInUse(ctx context.Context, k8sClient client.Client, namespace, roleName string) error {
	roleBindings := &rbacv1.RoleBindingList{}
	if err := k8sClient.List(ctx, roleBindings, client.InNamespace(namespace)); err != nil {
		return err
	}

	for _, rb := range roleBindings.Items {
		if rb.RoleRef.Kind == constants.RoleKind && rb.RoleRef.Name == roleName {
			return errors.NewFromCodeFormatMessage(errors.Var.RBACRoleInUse, roleName)
		}
	}

	return nil
}

// checkClusterRoleInUse 检查ClusterRole是否被绑定使用
func checkClusterRoleInUse(ctx context.Context, k8sClient client.Client, clusterRoleName string) error {
	// 检查ClusterRoleBinding
	clusterRoleBindings := &rbacv1.ClusterRoleBindingList{}
	if err := k8sClient.List(ctx, clusterRoleBindings); err != nil {
		return err
	}

	for _, crb := range clusterRoleBindings.Items {
		if crb.RoleRef.Kind == constants.ClusterRoleKind && crb.RoleRef.Name == clusterRoleName {
			return errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleInUse, clusterRoleName)
		}
	}

	// 检查RoleBinding（可能引用ClusterRole）
	roleBindings := &rbacv1.RoleBindingList{}
	if err := k8sClient.List(ctx, roleBindings); err != nil {
		return err
	}

	for _, rb := range roleBindings.Items {
		if rb.RoleRef.Kind == constants.ClusterRoleKind && rb.RoleRef.Name == clusterRoleName {
			return errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleInUse, clusterRoleName)
		}
	}

	return nil
}

// buildRoleRef 构建RoleRef
func buildRoleRef(roleName, roleKind string) rbacv1.RoleRef {
	return rbacv1.RoleRef{
		APIGroup: constants.RBACAPIGroup,
		Kind:     roleKind,
		Name:     roleName,
	}
}

// validatePolicyRules 验证策略规则
func validatePolicyRules(rules []rbacv1.PolicyRule) error {
	if len(rules) == 0 {
		return errors.NewFromCode(errors.Var.RBACInvalidPolicyRule)
	}

	for _, rule := range rules {
		if len(rule.Verbs) == 0 {
			return errors.NewFromCodeWithMessage(errors.Var.RBACInvalidPolicyRule, "verbs cannot be empty")
		}
	}

	return nil
}

// getGVRFromAPIResource 从API资源获取GroupVersionResource
func getGVRFromAPIResource(group, version, resource string) schema.GroupVersionResource {
	return schema.GroupVersionResource{
		Group:    group,
		Version:  version,
		Resource: resource,
	}
}
