package rbac

import (
	"context"
	"strings"

	"go.uber.org/zap"
	clusterClient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	rbacModels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	"k8s.io/client-go/discovery"
)

// GetAPIResources 获取集群可用API资源
func (h *handler) GetAPIResources(ctx context.Context, req *rbacModels.GetAPIResourcesRequest) (resp *rbacModels.GetAPIResourcesResponse, err error) {
	if l := logger.GetLogger(); l != nil {
		l.Info("Getting API resources",
			zap.String("cluster", req.ClusterID),
			zap.String("apiGroup", req.APIGroup),
			zap.String("keyword", req.Keyword),
		)
	}

	// 获取集群客户端
	cluster, err := clusterClient.GetCluster(req.ClusterID)
	if err != nil {
		return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterNotFound, req.ClusterID)
	}

	// 创建Discovery客户端
	discoveryClient, err := discovery.NewDiscoveryClientForConfig(cluster.GetClient().GetConfig())
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	// 获取API组列表
	apiGroupList, err := discoveryClient.ServerGroups()
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	var resourceGroups []rbacModels.APIResourceGroup
	var allVerbs []string

	// 遍历API组
	for _, group := range apiGroupList.Groups {
		// 如果指定了API组，则只处理该组
		if req.APIGroup != "" && group.Name != req.APIGroup {
			continue
		}

		// 获取组的首选版本
		if len(group.Versions) == 0 {
			continue
		}

		preferredVersion := group.PreferredVersion.Version
		if preferredVersion == "" && len(group.Versions) > 0 {
			preferredVersion = group.Versions[0].Version
		}

		// 获取该组版本的资源列表
		resourceList, err := discoveryClient.ServerResourcesForGroupVersion(group.PreferredVersion.GroupVersion)
		if err != nil {
			logger.GetLogger().Warn("Failed to get resources for group version",
				zap.String("groupVersion", group.PreferredVersion.GroupVersion),
				zap.Error(err),
			)
			continue
		}

		var resources []rbacModels.APIResource
		for _, resource := range resourceList.APIResources {
			// 跳过子资源（包含"/"的资源）
			if strings.Contains(resource.Name, "/") {
				continue
			}

			// 关键字过滤
			if req.Keyword != "" {
				if !strings.Contains(strings.ToLower(resource.Name), strings.ToLower(req.Keyword)) &&
					!strings.Contains(strings.ToLower(resource.Kind), strings.ToLower(req.Keyword)) {
					continue
				}
			}

			resources = append(resources, rbacModels.APIResource{
				Name:       resource.Name,
				Namespaced: resource.Namespaced,
				Kind:       resource.Kind,
			})

			// 收集所有动作
			for _, verb := range resource.Verbs {
				if !contains(allVerbs, verb) {
					allVerbs = append(allVerbs, verb)
				}
			}
		}

		if len(resources) > 0 {
			resourceGroups = append(resourceGroups, rbacModels.APIResourceGroup{
				Group:     group.Name,
				Version:   preferredVersion,
				Resources: resources,
			})
		}
	}

	// 处理核心API组（v1）
	if req.APIGroup == "" {
		coreResourceList, err := discoveryClient.ServerResourcesForGroupVersion("v1")
		if err == nil {
			var coreResources []rbacModels.APIResource
			for _, resource := range coreResourceList.APIResources {
				// 跳过子资源
				if strings.Contains(resource.Name, "/") {
					continue
				}

				// 关键字过滤
				if req.Keyword != "" {
					if !strings.Contains(strings.ToLower(resource.Name), strings.ToLower(req.Keyword)) &&
						!strings.Contains(strings.ToLower(resource.Kind), strings.ToLower(req.Keyword)) {
						continue
					}
				}

				coreResources = append(coreResources, rbacModels.APIResource{
					Name:       resource.Name,
					Namespaced: resource.Namespaced,
					Kind:       resource.Kind,
				})

				// 收集所有动作
				for _, verb := range resource.Verbs {
					if !contains(allVerbs, verb) {
						allVerbs = append(allVerbs, verb)
					}
				}
			}

			if len(coreResources) > 0 {
				resourceGroups = append([]rbacModels.APIResourceGroup{{
					Group:     "",
					Version:   "v1",
					Resources: coreResources,
				}}, resourceGroups...)
			}
		}
	}

	// 添加常用动作（确保包含所有标准动作）
	standardVerbs := []string{
		constants.VerbGet,
		constants.VerbList,
		constants.VerbWatch,
		constants.VerbCreate,
		constants.VerbUpdate,
		constants.VerbPatch,
		constants.VerbDelete,
		constants.VerbDeleteCollection,
		constants.VerbAll,
	}

	for _, verb := range standardVerbs {
		if !contains(allVerbs, verb) {
			allVerbs = append(allVerbs, verb)
		}
	}

	if l := logger.GetLogger(); l != nil {
		l.Info("API resources retrieved successfully",
			zap.String("cluster", req.ClusterID),
			zap.Int("groupCount", len(resourceGroups)),
			zap.Int("verbCount", len(allVerbs)),
		)
	}

	return &rbacModels.GetAPIResourcesResponse{
		Verbs:     allVerbs,
		Resources: resourceGroups,
	}, nil
}

// contains 检查字符串切片是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
