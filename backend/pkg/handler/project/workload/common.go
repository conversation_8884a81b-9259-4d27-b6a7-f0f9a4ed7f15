package workload

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	"k8s.io/apimachinery/pkg/runtime"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

func authWorkloadPermission(ctx context.Context, p common.ReaderAndWriterParam, verb string) error {
	authRequest := &rbac.AuthRequest{
		SelfSubjectAccessReview: &rbac.SelfSubjectAccessReviewRequest{
			Cluster:   p.GetCluster(),
			Namespace: p.GetNamespace(),
			Group:     p.GetGroup(),
			Version:   p.GetVersion(),
			Resource:  p.GetResourceType(),
			Verb:      verb,
		}}
	if err := rbac.AuthK8sRBAC(ctx, authRequest); err != nil {
		return err
	}
	return nil
}

// newWorkloadUnstructuredConverter ...
func newWorkloadUnstructuredConverter[T appsv1.Deployment |
	appsv1.StatefulSet | appsv1.DaemonSet | batchv1.Job | batchv1.CronJob]() *internalUnstructuredConverter[T] {
	return &internalUnstructuredConverter[T]{}
}

type internalUnstructuredConverter[T appsv1.Deployment |
	appsv1.StatefulSet | appsv1.DaemonSet | batchv1.Job | batchv1.CronJob] struct {
}

// FromUnstructured ...
func (t *internalUnstructuredConverter[T]) FromUnstructured(object ctrlclient.Object) (*T, error) {
	toUnstructured, err := runtime.DefaultUnstructuredConverter.ToUnstructured(object)
	if err != nil {
		return nil, err
	}
	obj := new(T)
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(toUnstructured, obj); err != nil {
		return nil, err
	}
	return obj, nil
}
