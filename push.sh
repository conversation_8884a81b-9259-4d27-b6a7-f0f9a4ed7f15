# /bin/bash



component_dirs=(
"olympus/components/caas"
"olympus/components/cache"
"olympus/components/cluster-init"
"olympus/components/database"
"olympus/components/databasedata"
"olympus/components/olympus"
"olympus/components/public"
)

solution_dirs=(
"olympus/solution/install"
"olympus/solution/uninstall"
)

chart_dir="charts"
# helm package
for component_dir in ${component_dirs[@]}; do
    component_chart_dir=$component_dir"/"$chart_dir
    if  test -d $component_chart_dir; then
      ls $component_chart_dir | \
          grep -v tgz | \
          awk '{print $1}' | \
          xargs -I {} /var/jenkins_home/helm package $component_chart_dir"/"{} -d $component_chart_dir
    fi

done

# push component
for component_dir in ${component_dirs[@]}; do
   ./trailer-linux-amd64  push component  $component_dir --config config.yaml
done

for solution_dir in ${solution_dirs[@]}; do
  ./trailer-linux-amd64  push solution   $solution_dir    --config config.yaml
done
