---
- vars:
    ansible_python_interpreter: /usr/bin/python3
    chart_version: 3.6.1
  block:
    # 导入olympus产品初始化数据库数据
    - name: <PERSON><PERSON> install olympus-product initdb
      kubernetes.core.helm:
        name: olympus-product-initdb
        chart_ref: hc-helm-repository/initdb
        chart_version: 3.6.1
        release_namespace: caas-system
        update_repo_cache: true
        values:
          global:
            registry: "{{ image.repository }}"
            databaseDriver: "{{ databaseDriver }}"
          useDatabaseDir: caas,oam,app_management,devops_code_inspect,devops_pipeline,apm
          envOption: cicd
    - name: Wait Job | import init data to mysql
      ansible.builtin.shell: |
        kubectl get job -n caas-system olympus-product-init-db-{{ chart_version }} -o jsonpath='{.status.conditions[?(@.type=="Complete")].status}'
      register: job_status
      until: job_status.stdout == "True"
      retries: 100
      delay: 5
      failed_when: job_status.stdout == "False"
      ignore_errors: true
    - name: Check Job Completed | init data job status
      ansible.builtin.shell: |
        if [ "`kubectl get job -n caas-system olympus-product-init-db-{{ chart_version }} -o jsonpath='{.status.conditions[?(@.type=="Complete")].status}'`" != "True" ];then echo "init data job not completed";exit 1;fi
