---
- name: He<PERSON> Upgrade caas-oam for enable disaster
  shell: |
    helm repo update && \
    helm upgrade -i -n caas-system caas-oam \
    hc-helm-repository/caas-oam \
    --version 3.6.1 --reuse-values \
    --set global.disaster.enable=true
- name: Helm Upgrade olympus-core for enable disaster
  shell: |
    helm repo update && \
    helm upgrade -i -n caas-system olympus-core \
    hc-helm-repository/olympus-core \
    --version 3.6.1 --reuse-values \
    --set global.disaster.enable=true
- name: Helm Upgrade olympus-portal for enable disaster
  shell: |
    helm repo update && \
    helm upgrade -i -n caas-system olympus-portal \
    hc-helm-repository/olympus-portal \
    --version 3.6.1 --reuse-values \
    --set global.disaster.enable=true,\
    global.stellaris.standby.address="{{ stellarisStandbyAddress }}",\
    global.stellaris.standby.port="{{ stellarisStandbyPort }}"