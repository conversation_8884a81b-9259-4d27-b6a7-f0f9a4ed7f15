- name: patch namespace cloudmonitor
  ansible.builtin.shell: |
    kubectl patch namespace cloudmonitor -p '{"apiVersion":"v1","kind":"Namespace","metadata":{"annotations":{"skyview-nodepool/ignore-all-daemonsets":"true","skyview/nodepool":"{{nodePoolName}}"},"finalizers":["skyview/nodepool"],"labels":{"kubernetes.io/metadata.name":"cloudmonitor","name":"cloudmonitor","skyview/nodepool":"{{nodePoolName}}"},"name":"cloudmonitor"}}'
    
