apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{include "cloudservice-operator.name" . }}
  labels:
    {{- include "cloudservice-operator.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - installer.unified-platform.harmonycloud.cn
    resources:
      - installers
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - installer.unified-platform.harmonycloud.cn
    resources:
      - installers/status
    verbs:
      - get
      - patch
      - update
  - apiGroups:
      - unified-platform.harmonycloud.cn
    resources:
      - cloudcomponents
      - cloudservices
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - oam.unified-platform.harmonycloud.cn
    resources:
      - alertpolicies
      - alertrules
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - stellaris.harmonycloud.cn
    resources:
      - clusters
    verbs:
      - get
      - list
      - watch
      - patch
      - update
  - apiGroups:
      - unified-platform.harmonycloud.cn
    resources:
      - cloudcomponents/status
      - cloudservices/status
    verbs:
      - get
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - namespaces
      - events
      - configmaps
    verbs:
      - get
      - list
      - watch
      - update
      - create
      - delete
      - patch
  - apiGroups:
      - ""
      - apps
      - batch
    resources:
      - pods
      - deployments
      - daemonsets
      - statefulsets
      - cronjobs
      - jobs
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - metrics.k8s.io
    resources:
      - pods
    verbs:
      - get
      - list
      - watch