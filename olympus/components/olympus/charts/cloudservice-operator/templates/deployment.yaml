apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "cloudservice-operator.name" . }}
  labels:
    {{- include "cloudservice-operator.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "cloudservice-operator.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "cloudservice-operator.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: webhook-certs
          secret:
            defaultMode: 420
            secretName: cloudservice-operator-cert
      serviceAccountName: {{ include "cloudservice-operator.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          env:
            - name: PORTAL_URL
              value: http://olympus-portal-svc:8080
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.global.registry }}/{{ .Values.image.name }}:{{ .Values.image.tag | default "latest" }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - mountPath: /tmp/k8s-webhook-server/serving-certs
              name: webhook-certs
              readOnly: true
          ports:
            {{- range $i, $p := .Values.service.ports }}
            - name: {{ $p.name }}
              containerPort: {{ $p.targetPort }}
              protocol: {{ $p.protocol }}
            {{- end }}
          livenessProbe:
            httpGet:
              path: /metrics
              port: {{ .Values.service.defaultPort }}
            failureThreshold: 3
            successThreshold: 1
            initialDelaySeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /metrics
              port: {{ .Values.service.defaultPort }}
            failureThreshold: 3
            successThreshold: 1
            initialDelaySeconds: 30
            timeoutSeconds: 10
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
