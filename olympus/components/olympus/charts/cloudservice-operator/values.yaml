# Default values for cloudservice-operator.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

global:
  registry: k8s-deploy

image:
  name: cloudservice-operator
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is latest
  tag: v3.6.1-566f50a

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "cloudservice-operator-sa"

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  defaultPort: 8080
  ports :
  - port: 8080
    name: metrics
    targetPort: 8080
    protocol: TCP
  - port: 9443
    name: webhook
    targetPort: 9443
    protocol: TCP

ingress:
  enabled: false

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
   limits:
     cpu: 500m
     memory: 512Mi
   requests:
     cpu: 100m
     memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
