kind: ConfigMap
apiVersion: v1
metadata:
  name: {{ .Values.name }}
data:
  application.yml: |-
    server:
      port: 8080

    spring:
      servlet:
        multipart:
          max-file-size: 500MB
          max-request-size: 500MB
      main:
        allow-bean-definition-overriding: true
      application:
        name: dop-pipeline
      redis:
{{ include "redis.templete" . | indent 8 }}
      datasource:
        dynamic:
          primary: master #设置默认的数据源或者数据源组,默认值即为master
          strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
          datasource:
            master:
{{ include "mysql.master.templete" . | indent 14 }}
            slave_1:
{{ include "mysql.slave1.templete" . | indent 14 }}
    project:
      mybatis-plus:
        sql-log: false
    mybatis-plus:
      mapper-locations: classpath*:/mapper/**/*Mapper.xml
      configuration:
        map-underscore-to-camel-case: true
        use-generated-keys: true
        default-fetch-size: 100
        default-statement-timeout: 30

    label:
      list-url: ${LABEL_URL:http://app-management-svc.caas-system:9060/}/tag/listTagByclassificationId
      save-url: ${LABEL_URL:http://app-management-svc.caas-system:9060/}/tag/create
      undefine-category: ${CLASSIFICATION_ID:ef9e2554-2956-47d8-a0bd-87ae19ade012}

    stage:
      sonar-url: http://devops-code-inspect-svc:8080/
      git-url: http://devops-scm-svc:8080/
      pipeline-url: http://devops-pipeline-svc:8080/
      warehouse-url: http://devops-repository-svc:8080/
      sub-system-url: http://devops-development-svc:8080/


    hc-tenant:
      mybatis-plus:
        enable: true
      biz:
        # 租户字段 默认tenant_id
        tenant-column: tenant_id
        # 租户字段类型 默认Long 还支持String
        tenant-field-type: Long
        # 全部是租户表
        all-tenant-tables: false
        tenant-tables:
          - devops_deploy_env

    pipeline:
      visual:
        enable: true
    jenkins:
      shared-library:
        url: http://gitlab:80/devops/shared-library.git
        credentials: 3d07ff58-2cd4-4939-b119-f44c32a6f7b6
        branch: master
        gitlabId: 2
    amp:
      url: http://app-management-svc.caas-system:9060/  #amp服务地址
      operation:
        openFlag: false #审计开发，true开启，false关闭
        level: info
    container:
      flag: {{ .Values.container.flag }}
    xxl:
      job:
        enabled: false
        admin:
          addresses: http://devops-xxl-job:8080/xxl-job-admin
          #addresses: http://************:8080/xxl-job-admin
        #执行器通讯TOKEN [选填]：非空时启用；
        executor:
          accessToken:
          #执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
          appname: devops-pipeline-svc
          #执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题
          address:
          #执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"
          ip:
      #执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口
          port: 8000
          #执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径
          logpath: /tmp/log
          #执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能
          logretentiondays: 30
    development:
      url: ${stage.sub-system-url}