global:
  registry: k8s-deploy
  f5Address: **********
  disaster:
    enable: false

  mysql:
    serviceName: caas-mysql
    port: 3306
  redis:
    serviceName: api-redis-service

caas_core:
  volume:
    storageClassName: default
    size: 10Gi
  replicaCount: 1
  image: caas-core
  tag: v3.6.1-e2501c33a
  pullPolicy: Always
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 2
      memory: 4Gi
