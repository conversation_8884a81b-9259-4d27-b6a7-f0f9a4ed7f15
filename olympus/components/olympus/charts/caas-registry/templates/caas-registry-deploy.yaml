apiVersion: apps/v1
kind: Deployment
metadata:
  name: caas-registry
  labels:
    app: caas-registry
spec:
  progressDeadlineSeconds: 600
  replicas: {{ .Values.caas_registry.replicaCount }}
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: caas-registry
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: caas-registry
    spec:
      automountServiceAccountToken: true
      serviceAccountName: caas-registry-rbac
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 70
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                  - key: app
                    operator: In
                    values:
                    - caas-registry
                topologyKey: kubernetes.io/hostname
      containers:
        - command:
            - java
          args:
            - -server
            - -XX:+UseContainerSupport
            - -XX:MaxRAMPercentage=75.0
            - -XX:InitialRAMPercentage=75.0
            - -XX:MinRAMPercentage=75.0
            - -jar
            - /caas-registry.jar
            - --spring.config.location=/cfg/application.yml
          env:
            - name: mysql_username
              valueFrom:
                secretKeyRef:
                  key: username
                  name: olympus-mysql-secret
            - name: mysql_password
              valueFrom:
                secretKeyRef:
                  key: password
                  name: olympus-mysql-secret
            - name: redis_password
              valueFrom:
                secretKeyRef:
                  key: password
                  name: olympus-redis-secret
            - name: aliyun_logs_logstash
              value: /logs/*
            - name: aliyun_logs_logstash_tags
              value: k8s_resource_type=Deployment,k8s_resource_name=caas-registry
            - name: TZ
              value: Asia/Shanghai
            - name: aliyun_logs_stdout
              value: stdout
            - name: aliyun_logs_stdout_tags
              value: k8s_resource_type=Deployment,k8s_resource_name=caas-registry,encoding=UTF-8
          image: {{ .Values.global.registry }}/{{ .Values.caas_registry.image }}:{{ .Values.caas_registry.tag }}
          imagePullPolicy:  {{ .Values.caas_registry.pullPolicy }}
          securityContext:
            privileged: true
            capabilities:
              add:
              - SYS_ADMIN
              - MKNOD
              - SETFCAP
          livenessProbe:
            failureThreshold: 5
            httpGet:
              path: /openapi/healthy
              port: api
              scheme: HTTP
            initialDelaySeconds: 180
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: caas-registry
          ports:
            - containerPort: 8080
              name: api
              protocol: TCP
          readinessProbe:
            failureThreshold: 5
            httpGet:
              path: /openapi/healthy
              port: api
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            requests:
              cpu: {{ .Values.caas_registry.resources.requests.cpu }}
              memory: {{ .Values.caas_registry.resources.requests.memory }}
            limits:
              cpu: {{ .Values.caas_registry.resources.limits.cpu }}
              memory: {{ .Values.caas_registry.resources.limits.memory }}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /logs/
              name: logdir
            - mountPath: /cfg/application.yml
              name: caas-registry-cm
              subPath: application.yml
            - mountPath: /usr/local/caas-registry
              name: caas-registry-upload
            - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              name: kube-api-access
              readOnly: true
            - mountPath: /var/lib/containers/storage
              name: system-storage
            - mountPath: /error-num-language-pkg
              name: olympus-exception-translate
            - mountPath: /k8s-error-message
              name: olympus-k8s-error-message
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - configMap:
            name: olympus-k8s-error-message
          name: olympus-k8s-error-message
        - configMap:
            name: olympus-exception-translate
          name: olympus-exception-translate
        - emptyDir: {}
          name: system-storage
        - configMap:
            defaultMode: 420
            items:
              - key: application.yml
                path: application.yml
            name: caas-registry-cm
          name: caas-registry-cm
        - emptyDir: {}
          name: caas-registry-upload
        - emptyDir: {}
          name: logdir
        - name: kube-api-access
          secret:
            defaultMode: 420
            secretName: olympus-sa-secret