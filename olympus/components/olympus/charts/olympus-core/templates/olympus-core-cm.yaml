apiVersion: v1
kind: ConfigMap
metadata:
  name: olympus-core-cm
data:
  application.yml: |-
    translate:
      error-config-path: /error-num-language-pkg
    springfox:
      documentation:
        enabled: false
        swagger-ui:
          enabled: false
    amp:
      url: http://app-management-svc:9060
      operation:
        component: unified-platform
    spring:
      redis:
        host:  {{ .Values.global.redis.serviceName }}
        password: ${redis_password}
        port: 6379
        database: 2
        jedis:
          pool:
            max-active: 16
            min-idle: 8
            max-wait: 12
            time-between-eviction-runs: 10m
      datasource:
        username: ${mysql_username}
        password: ${mysql_password}
        url: jdbc:mysql://{{ .Values.global.mysql.serviceName }}:{{ .Values.global.mysql.port }}/caas?useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai
        driver-class-name: com.mysql.cj.jdbc.Driver
        druid:
          driver-class-name: com.mysql.cj.jdbc.Driver
          initialSize: 4
          maxActive: 16
          minIdle: 4
          maxWait: 60000
          validationQuery: SELECT 1 FROM DUAL
          testOnBorrow: true
      web:
        resources:
          static-locations: file:/usr/local/olympus-core,classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/
      servlet:
        multipart:
          # 上传文件的默认大小
          max-file-size: 20MB
          # 一次上传文件的总请求大小
          max-request-size: 50MB
    feign:
      hystrix:
        enabled: false
      client:
        config:
          default:
            connectTimeout: 30000
            readTimeout: 30000
    ribbon:
      ReadTimeout: 10000
      ConnectTimeout: 5000
    mybatis-plus:
      mapper-locations: classpath:mapping/*.xml
      typeEnumsPackage: com.skyview.caas.common.enums
    logging:
      config: /logback.xml
      level:
        com.harmonycloud.tool.api: info
        com.harmonycloud.caas.filters: info
        root: info
        com.skyview.tool.api.client: info
    server:
      port: 8080
    devops:
      amp:
        url: http://app-management-svc:9060
    caas:
      amp:
        url: http://olympus-core-svc:8080
      registry:
        url: http://caas-registry-svc:8080
      core:
        upload:
          path: /usr/local/olympus-core
    management:
      enabled-by-default: false
      endpoints:
        web:
          exposure:
            include: "health"
      health:
        enabled: true
        elasticsearch:
          enabled: false
    grafana:
      user: ${grafana_user}
      password: ${grafana_password}
    es:
      backup:
        path: /data-backup
      system:
        protocol: http
        host: {{ .Values.global.f5Address }}
        port: 29010
        esName: ${es_esName}
        esPwd: ${es_esPwd}
    monitoring:
      prometheus:
        protocol: http
        ip: {{ .Values.global.f5Address }}
        port: 29000
    task:
      overview:
        resource: 0 0/5 * * * ? # 每5分钟一刷新
        project-registry: 0 0 2 * * ?
        system-registry: 0 0 3 * * ?
    disaster:
      recovery: {{ .Values.global.disaster.enable }}
    cluster:
      proxy-name: stellaris-proxy
      namespace: stellaris-system
    storage:
      image:
        # adapt 1.27 kubernetes cluster
        nfs-provsioner: k8s-deploy/nfs-subdir-external-provisioner:v4.0.2