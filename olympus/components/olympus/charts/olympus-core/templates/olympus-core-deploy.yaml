apiVersion: apps/v1
kind: Deployment
metadata:
  name: olympus-core
  labels:
    app: olympus-core
spec:
  progressDeadlineSeconds: 600
  replicas: {{ .Values.olympus_core.replicaCount }}
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: olympus-core
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: olympus-core
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 70
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                  - key: app
                    operator: In
                    values:
                    - olympus-core
                topologyKey: kubernetes.io/hostname
      containers:
        - command:
            - java
          args:
            - -server
            - -XX:+UseContainerSupport
            - -XX:MaxRAMPercentage=75.0
            - -XX:InitialRAMPercentage=75.0
            - -XX:MinRAMPercentage=75.0
            - -jar
            - /usr/local/olympus-core.jar
            - --spring.config.location=/usr/local/conf/application.yml
          env:
            - name: mysql_username
              valueFrom:
                secretKeyRef:
                  key: username
                  name: olympus-mysql-secret
            - name: mysql_password
              valueFrom:
                secretKeyRef:
                  key: password
                  name: olympus-mysql-secret
            - name: redis_password
              valueFrom:
                secretKeyRef:
                  key: password
                  name: olympus-redis-secret
            - name: aliyun_logs_logstash
              value: /logs/*
            - name: aliyun_logs_logstash_tags
              value: k8s_resource_type=Deployment,k8s_resource_name=olympus-core
            - name: TZ
              value: Asia/Shanghai
          image: {{ .Values.global.registry }}/{{ .Values.olympus_core.image }}:{{ .Values.olympus_core.tag }}
          imagePullPolicy: {{ .Values.olympus_core.pullPolicy }}
          lifecycle:
            postStart:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - if [ ! -d '/usr/local/olympus-core/images' ]; then cp -r /usr/local/olympus-core-pv/images
                    /usr/local/olympus-core/ ;fi && cp -r /usr/local/olympus-core-pv/doc /usr/local/olympus-core/ && cp /usr/local/olympus-core-pv/images/cluster/deploy-proxy.sh /usr/local/olympus-core/images/cluster/deploy-proxy.sh
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /openapi/healthy
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 180
            periodSeconds: 20
            successThreshold: 1
            timeoutSeconds: 10
          name: olympus-core
          ports:
            - containerPort: 8080
              name: api
              protocol: TCP
          readinessProbe:
            failureThreshold: 5
            httpGet:
              path: /openapi/healthy
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            timeoutSeconds: 10
          resources:
            requests:
              cpu: {{ .Values.olympus_core.resources.requests.cpu }}
              memory: {{ .Values.olympus_core.resources.requests.memory }}
            limits:
              cpu: {{ .Values.olympus_core.resources.limits.cpu }}
              memory: {{ .Values.olympus_core.resources.limits.memory }}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /usr/local/conf/application.yml
              name: olympus-core-cm
              subPath: application.yml
            - mountPath: /var/run
              name: docker
            - mountPath: /logs
              name: logdir
            - mountPath: /usr/local/olympus-core
              name: olympus-core-pvc
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - configMap:
            defaultMode: 420
            items:
              - key: application.yml
                path: application.yml
            name: olympus-core-cm
          name: olympus-core-cm
        - hostPath:
            path: /var/run
            type: ""
          name: docker
        - emptyDir: {}
          name: logdir
        - name: olympus-core-pvc
          persistentVolumeClaim:
            claimName: olympus-core-pvc