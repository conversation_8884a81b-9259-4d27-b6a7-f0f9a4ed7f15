global:
  registry: k8s-deploy
  f5Address: **********
  disaster:
    enable: false
  mysql:
    serviceName: caas-mysql
    port: 3306
  redis:
    serviceName: api-redis-service
    port: 6379
olympus_core:
  volume:
    storageClassName: default
    size: 10Gi
  replicaCount: 1
  image: olympus-core
  tag: v3.4.4-4e2a628e5
  pullPolicy: Always
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 2
      memory: 4Gi
