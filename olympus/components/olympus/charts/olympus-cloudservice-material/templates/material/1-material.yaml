apiVersion: cloud-service.harmonycloud.cn/v1alpha1
kind: Material
metadata:
  name: olympus-unified-platform-1
  annotations:
    message.material.harmonycloud.cn/reapply: "true"
spec:
  cloudServiceName: unified-platform
  waitPolicy:
    onSuccess:
      - name: olympus-unified-platform-0
  tasks:
    # 基座平台 - 资源中心菜单
    - name: permission_resource_center
      kind: permissionImport
      permissionConfig:
        app:
          code: application
        permissions: |-
          [{"name":"资源中心","code":"unified_platform_sys_platform_mgr","kind":"platform","icon":"v35_Resources","url":"/quotacenter/space/overview","type":"menu","method":"get","sort":2,"annotation":{"ceiling":true,"group":"base"},"children":[{"parent":{"code":"unified_platform_sys_platform_mgr"},"name":"集群管理","code":"unified_platform_sys_cluster_manage","kind":"platform","icon":"v35_Clusters","url":"/cluster/set/clusterlist","type":"menu","method":"get","annotation":{"ceiling":true,"resources":["stellaris.harmonycloud.cn/clusters","installer.unified-platform.harmonycloud.cn/installers"],"titleDescription":"多集群纳管等统一管理中心"},"children":[{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"集群管理详情","code":"unified_platform_sys_cluster_manage_detail","inVisible":true,"kind":"platform","icon":"cluster-menu","url":"/cluster/set/clusterlist","type":"menu","method":"get","annotation":{"ceiling":true,"resources":["stellaris.harmonycloud.cn/clusters","installer.unified-platform.harmonycloud.cn/installers"],"titleDescription":"多集群纳管等统一管理中心"},"children":[{"parent":{"code":"unified_platform_sys_cluster_manage_detail"},"name":"集群总览","code":"unified_platform_sys_cluster_overview","kind":"platform","icon":"cluster-space-overview-menu","url":"/cluster/space/ClusterOverview","type":"menu","method":"get","annotation":{"resources":["heimdallr.harmonycloud.cn/networkdetails","nodes","batch/cronjobs","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/networkresources","secrets","configmaps","heimdallr.harmonycloud.cn/hdareas","apps/statefulsets","namespaces","apps/replicasets","heimdallr.harmonycloud.cn/hdblocks","persistentvolumeclaims","apps/deployments","heimdallr.harmonycloud.cn/hdpools","pods","storage.k8s.io/storageclasses","serviceaccounts","persistentvolumes","apps/daemonsets","batch/jobs","replicationcontrollers","resourcequotas","harmonycloud.cn/nodepools","services"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_overview"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"replicationcontrollers":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_cluster_manage_detail"},"name":"主机管理","code":"unified_platform_sys_node_manage","kind":"platform","icon":"nodes-menu","url":"","type":"menu","method":"get","children":[{"parent":{"code":"unified_platform_sys_node_manage"},"name":"普通视角","code":"unified_platform_sys_node_manage_normal","kind":"platform","url":"/cluster/space/node/normal","type":"menu","method":"get","annotation":{"resources":["nodes","stellaris.harmonycloud.cn/clusters","namespaces","events","isolate.harmonycloud.cn/isolatelocks","isolate.harmonycloud.cn/hleases","harmonycloud.cn/nodepools","pods","stellaris.harmonycloud.cn/clustertopologies","stellaris.harmonycloud.cn/servicesyncs","services"]},"children":[{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/nodes/{nodeName}/resources,/clusters/{clusterName}/nodes/{nodeName}/nodeInfo,/clusters/{clusterName}/nodes/{nodeName}/events","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"新增主机","code":"add","kind":"platform","url":"/clusters/{clusterName}/nodes","type":"permission","method":"post","annotation":{"component":"node-up-down","componentName":"主机上下线"}},{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"主机维护","code":"nodeMaintain","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["create","delete","get","list"],"isolate.harmonycloud.cn/hleases":["delete","get","patch","create","update"],"isolate.harmonycloud.cn/isolatelocks":["delete","get","patch","create","update"],"namespaces":["create","delete","get","patch","update"],"nodes":["create","get","list","patch","update"],"stellaris.harmonycloud.cn/clusters":["delete","watch","get","list","patch","create","update"]}}},{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"应用迁移","code":"nodeDrain","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["create","delete","get","list"],"isolate.harmonycloud.cn/hleases":["delete","get","patch","create","update"],"isolate.harmonycloud.cn/isolatelocks":["delete","get","patch","create","update"],"namespaces":["create","delete","get","patch","update"],"nodes":["create","get","list","patch","update"],"stellaris.harmonycloud.cn/clusters":["delete","watch","get","list","patch","create","update"]}}},{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"主机隔离","code":"nodeIsolate","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["create","delete","get","list"],"isolate.harmonycloud.cn/hleases":["delete","get","patch","create","update"],"isolate.harmonycloud.cn/isolatelocks":["delete","get","patch","create","update"],"namespaces":["create","delete","get","patch","update"],"nodes":["create","get","list","patch","update"],"stellaris.harmonycloud.cn/clusters":["delete","watch","get","list","patch","create","update"]}}},{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"调度","code":"dispatch","kind":"platform","url":"/clusters/{clusterName}/nodes/{nodeName}/cordon,/clusters/{clusterName}/nodes/{nodeName}/uncordon","type":"permission","method":"put","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list","patch","update"],"isolate.harmonycloud.cn/isolatelocks":["get","list","patch","update","delete"],"namespaces":["get","list"],"nodes":["get","list","patch","update"],"pods":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"编辑注解","code":"editAnnotations","kind":"platform","url":"/clusters/{clusterName}/nodes/{nodeName}/annotations","type":"permission","method":"put","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","patch","update"],"pods":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"编辑标签","code":"editLabel","kind":"platform","url":"/clusters/{clusterName}/nodes/{nodeName}/labels","type":"permission","method":"put","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","patch","update"],"pods":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"编辑污点","code":"editTaint","kind":"platform","url":"/clusters/{clusterName}/nodes/{nodeName}/taints","type":"permission","method":"put","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","patch","update"],"pods":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"主机下线","code":"nodeOffLine","kind":"platform","url":"/clusters/{clusterName}/nodes/drain","type":"permission","method":"post","annotation":{"component":"node-up-down","componentName":"主机上下线","resource_option":{"nodes":["get","list","patch","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_manage_normal"},"name":"主机监控","code":"skyview_node_monitor","kind":"platform","type":"permission","method":"get","annotation":{"component":"monitoring","componentName":"监控"}}]},{"parent":{"code":"unified_platform_sys_node_manage"},"name":"资源池视角","code":"unified_platform_sys_node_manage_resource_pool","kind":"platform","url":"/cluster/space/node/resourcepool","type":"menu","method":"get","annotation":{"resources":["nodes","stellaris.harmonycloud.cn/clusters","namespaces","events","isolate.harmonycloud.cn/isolatelocks","isolate.harmonycloud.cn/hleases","harmonycloud.cn/nodepools","pods"]},"children":[{"parent":{"code":"unified_platform_sys_node_manage_resource_pool"},"name":"查询","code":"query","kind":"platform","url":"/clusters/nodepools","type":"permission","method":"get","annotation":{"component":"node-pool","componentName":"资源池","resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]}]},{"parent":{"code":"unified_platform_sys_cluster_manage_detail"},"name":"命名空间","code":"unified_platform_sys_cluster_namespace_manager","kind":"platform","icon":"namespace-menu","url":"/cluster/space/namespace","type":"menu","method":"get","annotation":{"resources":["apps/statefulsets","pods","namespaces","batch/jobs","resourcequotas","heimdallr.harmonycloud.cn/networkresources","nodes","stellaris.harmonycloud.cn/clusters","events","harmonycloud.cn/nodepools","heimdallr.harmonycloud.cn/hdareas","isolate.harmonycloud.cn/isolatelocks","apps/deployments","persistentvolumeclaims","storage.k8s.io/storageclasses","heimdallr.harmonycloud.cn/hdblocks","apps/daemonsets","batch/cronjobs","isolate.harmonycloud.cn/hleases","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/hdsvcs","heimdallr.harmonycloud.cn/networkdetails"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_namespace_manager"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/namespaces","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_namespace_manager"},"name":"新增","code":"add","kind":"platform","url":"/clusters/{clusterName}/namespaces","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list","patch","create","update"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","patch","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_namespace_manager"},"name":"编辑元数据","code":"edit","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespaces}","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list","patch","create","update"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","patch","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_namespace_manager"},"name":"编辑描述","code":"editDescription","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespaces}/description","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list","patch","create","update"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","patch","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_namespace_manager"},"name":"删除","code":"remove","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespaces}","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list","patch","create","update","delete"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","patch","create","update","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_namespace_manager"},"name":"分配项目","code":"allocate_project","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list","update"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","create"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_cluster_manage_detail"},"name":"集群组件","code":"unified_platform_sys_cluster_component","kind":"platform","icon":"components-menu","url":"/cluster/space/clusterComp","type":"menu","method":"get","annotation":{"resources":["pods","resourcequotas","stellaris.harmonycloud.cn/clusters","events","namespaces","nodes"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_component"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_component"},"name":"组件详情","code":"detail","kind":"platform","url":"/clusters/{clusterName}/components/{componentName}","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_cluster_component"},"name":"批量新增","code":"batch","kind":"platform","url":"/clusters/{clusterName}/components/switches","type":"permission","method":"post"},{"parent":{"code":"unified_platform_sys_cluster_component"},"name":"编辑","code":"edit","kind":"platform","url":"/clusters/{clusterName}/components/{componentName}","type":"permission","method":"put","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list","patch","update"]}}},{"parent":{"code":"unified_platform_sys_cluster_component"},"name":"自动识别","code":"autoRecognition","kind":"platform","url":"/clusters/{clusterName}/components/scan","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list","patch","update"]}}},{"parent":{"code":"unified_platform_sys_cluster_component"},"name":"接入","code":"join","kind":"platform","url":"/clusters/{clusterName}/components","type":"permission","method":"post","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list","patch","update"]}}},{"parent":{"code":"unified_platform_sys_cluster_component"},"name":"取消接入","code":"remove","kind":"platform","url":"/clusters/{clusterName}/components/{componentName}","type":"permission","method":"delete","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list","patch","update"]}}}]},{"parent":{"code":"unified_platform_sys_cluster_manage_detail"},"name":"工作负载","code":"unified_platform_sys_clusters_workloads","kind":"platform","icon":"load-menu","url":"","type":"menu","method":"get","children":[{"parent":{"code":"unified_platform_sys_clusters_workloads"},"name":"无状态部署","code":"unified_platform_sys_clusters_deployment","kind":"platform","url":"/cluster/space/resource/deployment/list","type":"menu","method":"get","annotation":{"resources":["apps/deployments","apps/replicasets","events","namespaces","pods","resourcequotas","stellaris.harmonycloud.cn/clusters","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"parent":{"code":"unified_platform_sys_clusters_deployment"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/deployments,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment},/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/describe,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/events,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/metadata,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/pods,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets/{replicaset}/yaml,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/yaml","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_deployment"},"name":"新增","code":"skyview_deployment_add","kind":"platform","url":"/clusters/{clusterName}/deployments","type":"permission","method":"post","annotation":{"resource_option":{"apps/deployments":["get","list","create","update"],"apps/replicasets":["get","list","create","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_deployment"},"name":"编辑副本数","code":"skyview_replicas_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","create","update"],"apps/replicasets":["get","list","create","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_deployment"},"name":"编辑元数据","code":"skyview_deployment_metadata_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","create","update"],"apps/replicasets":["get","list","create","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_deployment"},"name":"版本管理","code":"skyview_deployment_version","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","create","update"],"apps/replicasets":["get","list","create","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_deployment"},"name":"查看版本yaml","code":"skyview_deployment_yaml_check","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_deployment"},"name":"yaml对比","code":"skyview_deployment_yaml_compare","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_deployment"},"name":"版本回滚","code":"skyview_deployment_version_rollback","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/revisions/{revision}/rollback","type":"permission","method":"post","annotation":{"resource_option":{"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_deployment"},"name":"编辑yaml","code":"skyview_deployment_yaml_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","update"],"apps/replicasets":["get","list","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_deployment"},"name":"删除","code":"skyview_deployment_remove","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}","type":"permission","method":"delete","annotation":{"resource_option":{"apps/deployments":["get","list","delete"],"apps/replicasets":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_clusters_workloads"},"name":"有状态部署","code":"unified_platform_sys_clusters_statefulset","kind":"platform","url":"/cluster/space/resource/statefulset/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","events","resourcequotas","apps/statefulsets","pods","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"parent":{"code":"unified_platform_sys_clusters_statefulset"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset},/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/describe,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/events,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/metadata,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/pods,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/yaml,/clusters/{clusterName}/statefulsets","type":"permission","method":"get","annotation":{"resource_option":{"apps/statefulsets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_statefulset"},"name":"新增","code":"skyview_statefulset_add","kind":"platform","url":"/clusters/{clusterName}/statefulsets","type":"permission","method":"post","annotation":{"resource_option":{"apps/statefulsets":["get","list","create","update"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_statefulset"},"name":"编辑副本数","code":"skyview_replicas_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/statefulsets":["get","list","create","update","patch"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_statefulset"},"name":"编辑元数据","code":"skyview_statefulset_metadata_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/statefulsets":["get","list","create","update","patch"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_statefulset"},"name":"编辑yaml","code":"skyview_statefulset_yaml_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/statefulsets":["get","list","create","update","patch"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_statefulset"},"name":"删除","code":"skyview_statefulset_remove","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}","type":"permission","method":"delete","annotation":{"resource_option":{"apps/statefulsets":["get","list","delete"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_clusters_workloads"},"name":"守护进程","code":"unified_platform_sys_clusters_daemonset","kind":"platform","url":"/cluster/space/resource/daemonset/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","apps/daemonsets","pods","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"parent":{"code":"unified_platform_sys_clusters_daemonset"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/daemonsets,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset},/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/describe,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/events,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/metadata,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/pods,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/yaml","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_daemonset"},"name":"新增","code":"skyview_daemonset_add","kind":"platform","url":"/clusters/{clusterName}/daemonsets","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list","create","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_daemonset"},"name":"编辑元数据","code":"skyview_daemonset_metadata_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_daemonset"},"name":"编辑yaml","code":"skyview_daemonset_yaml_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_daemonset"},"name":"删除","code":"skyview_daemonset_remove","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}","type":"permission","method":"delete","annotation":{"resource_option":{"apps/daemonsets":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_clusters_workloads"},"name":"普通任务","code":"unified_platform_sys_clusters_job","kind":"platform","url":"/cluster/space/resource/job/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","batch/jobs","pods","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"parent":{"code":"unified_platform_sys_clusters_job"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/jobs,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/describe,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/events,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/info,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/metadata,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/pods,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/yaml","type":"permission","method":"get","annotation":{"resource_option":{"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_job"},"name":"新增","code":"skyview_job_add","kind":"platform","url":"/clusters/{clusterName}/jobs","type":"permission","method":"post","annotation":{"resource_option":{"batch/jobs":["get","list","create"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_job"},"name":"重新执行","code":"skyview_job_restart","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/start","type":"permission","method":"post","annotation":{"resource_option":{"batch/jobs":["get","list","create","update","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_job"},"name":"编辑元数据","code":"skyview_job_metadata_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"batch/jobs":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_job"},"name":"编辑yaml","code":"skyview_job_yaml_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"batch/jobs":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_job"},"name":"删除","code":"skyview_job_remove","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}","type":"permission","method":"delete","annotation":{"resource_option":{"batch/jobs":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_clusters_workloads"},"name":"定时任务","code":"unified_platform_sys_clusters_cronjob","kind":"platform","url":"/cluster/space/resource/cronjob/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","batch/cronjobs","pods","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"parent":{"code":"unified_platform_sys_clusters_cronjob"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/cronjobs,/clusters/{clusterName}/namespaces/{namespace}/cronjob/{cronjobName}/yaml,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/describe,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/events,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/info,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/jobs,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/metadata","type":"permission","method":"get","annotation":{"resource_option":{"batch/cronjobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_cronjob"},"name":"新增","code":"add","kind":"platform","url":"/clusters/{clusterName}/cronjobs","type":"permission","method":"post","annotation":{"resource_option":{"batch/cronjobs":["get","list","create"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_cronjob"},"name":"启停","code":"schedule","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}","type":"permission","method":"put","annotation":{"resource_option":{"batch/cronjobs":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_cronjob"},"name":"编辑元数据","code":"editMetaData","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/metadata","type":"permission","method":"put","annotation":{"resource_option":{"batch/cronjobs":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_cronjob"},"name":"编辑策略","code":"editInfo","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/cronjob/{cronjobName}","type":"permission","method":"post","annotation":{"resource_option":{"batch/cronjobs":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_cronjob"},"name":"编辑yaml","code":"editYaml","kind":"platform","url":"/clusters/{clusterName}/cronjob/yaml","type":"permission","method":"put","annotation":{"resource_option":{"batch/cronjobs":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_cronjob"},"name":"删除执行记录","code":"deleteJob","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}","type":"permission","method":"delete","annotation":{"resource_option":{"batch/cronjobs":["get","list","create","update","patch","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_cronjob"},"name":"删除","code":"delete","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/cronjob/{cronjobName}","type":"permission","method":"delete","annotation":{"resource_option":{"batch/cronjobs":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_clusters_workloads"},"name":"Pod容器组","code":"unified_platform_sys_clusters_pod_container_group","kind":"platform","url":"/cluster/space/resource/pod","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","harmonycloud.cn/nodepools","events","pods","nodes"]},"children":[{"parent":{"code":"unified_platform_sys_clusters_pod_container_group"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/pods/filter,/clusters/{clusterName}/pods,/clusters/{clusterName}/namespaces/{namespace}/pods/{podName},/clusters/{clusterName}/namespaces/{namespace}/pods/{podName}/containers","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_pod_container_group"},"name":"文件路径查询","code":"pod_file_pwd","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"pods":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_pod_container_group"},"name":"文件下载","code":"pod_file_download","kind":"platform","url":"/clusters/{clusterName}/pods/filter,/clusters/{clusterName}/pods,/clusters/{clusterName}/namespaces/{namespace}/pods/{podName},/clusters/{clusterName}/namespaces/{namespace}/pods/{podName}/containers","type":"permission","method":"get","annotation":{"resource_option":{"pods":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_pod_container_group"},"name":"监控","code":"monitor","kind":"platform","url":"/cluster/{clusterName}/namespace/{namespaces}/getPodResource","type":"permission","method":"get","annotation":{"component":"monitoring","componentName":"监控","resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_pod_container_group"},"name":"pod日志","code":"containerLog","kind":"platform","url":"/clusters/{clusterName}/namespaces/{namespace}/pods/{podName}/containers/{container}/stderrlogs","type":"permission","method":"patch","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list","watch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_pod_container_group"},"name":"pod控制台","code":"containerTerminal","kind":"platform","url":"/podTerminal","type":"permission","method":"patch","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_pod_container_group"},"name":"事件","code":"event","kind":"platform","url":"/cluster/{clusterName}/namespaces/{namespace}/pods/{podName}/events","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_pod_container_group"},"name":"查看yaml","code":"yaml","kind":"platform","url":"/cluster/{clusterName}/namespaces/{namespace}/pods/{podName}/yaml","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_clusters_pod_container_group"},"name":"删除","code":"remove","kind":"platform","url":"/cluster/{clusterName}/namespaces/{namespace}/pods/{podName}","type":"permission","method":"delete","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]}]},{"parent":{"code":"unified_platform_sys_cluster_manage_detail"},"name":"配置挂载","code":"unified_platform_sys_cluster_config_mount","kind":"platform","icon":"configure-mount-menu","url":"","type":"menu","method":"get","children":[{"parent":{"code":"unified_platform_sys_cluster_config_mount"},"name":"配置文件","code":"unified_platform_sys_cluster_configmap","kind":"platform","url":"/cluster/space/configMap/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings","events","pods","apps/deployments","apps/statefulsets","apps/daemonsets","apps/replicasets","batch/jobs","batch/cronjobs","configmaps"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_configmap"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list","watch"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_configmap"},"name":"新增","code":"skyview_configmap_add","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list","create"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_configmap"},"name":"编辑挂载数据","code":"skyview_configmap_data_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_configmap"},"name":"编辑元数据","code":"skyview_configmap_meta_data_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_configmap"},"name":"编辑Yaml","code":"skyview_configmap_yaml_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_configmap"},"name":"删除","code":"skyview_configmap_remove","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_cluster_config_mount"},"name":"保密字典","code":"unified_platform_sys_cluster_secret","kind":"platform","url":"/cluster/space/secret/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","pods","apps/deployments","apps/statefulsets","apps/daemonsets","apps/replicasets","batch/jobs","batch/cronjobs","secrets","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_secret"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_secret"},"name":"新增","code":"skyview_secret_add","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_secret"},"name":"编辑加密数据","code":"skyview_secret_data_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_secret"},"name":"编辑元数据","code":"skyview_secret_meta_data_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_secret"},"name":"编辑Yaml","code":"skyview_secret_yaml_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_secret"},"name":"删除","code":"skyview_secret_remove","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]}]},{"parent":{"code":"unified_platform_sys_cluster_manage_detail"},"name":"存储","code":"unified_platform_sys_cluster_storage_service","kind":"platform","icon":"storage-menu","url":"","type":"menu","method":"get","children":[{"parent":{"code":"unified_platform_sys_cluster_storage_service"},"name":"存储卷声明","code":"unified_platform_sys_cluster_pvc","kind":"platform","url":"/cluster/space/pvc/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","pods","storage.k8s.io/storageclasses","persistentvolumeclaims","persistentvolumes","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_pvc"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/pvc,/clusters/{clusterName}/pvc/{pvcName}/describe,/clusters/{clusterName}/pvc/{pvcName}/events,/clusters/{clusterName}/pvc/{pvcName}/info,/clusters/{clusterName}/pvc/{pvcName}/pods,/clusters/{clusterName}/pvc/{pvcName}/yaml,/clusters/{clusterName}/pvc/available","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_pvc"},"name":"新增","code":"skyview_pvc_add","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","create"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_pvc"},"name":"扩容","code":"skyview_pvc_expand","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","update","patch"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_pvc"},"name":"编辑元数据","code":"skyview_pvc_meta_data_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","update","patch"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_pvc"},"name":"编辑Yaml","code":"skyview_pvc_yaml_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","update","patch"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_pvc"},"name":"删除","code":"skyview_pvc_remove","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","delete"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_cluster_storage_service"},"name":"存储卷","code":"unified_platform_sys_cluster_pv","kind":"platform","url":"/cluster/space/pv/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","pods","storage.k8s.io/storageclasses","persistentvolumeclaims","persistentvolumes"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_pv"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/persistentvolume,/clusters/{clusterName}/persistentvolume/{pvName},/clusters/{clusterName}/persistentvolume/bound/pods,/clusters/{clusterName}/persistentvolume/custom/storage,/clusters/{clusterName}/persistentvolume/describe,/clusters/{clusterName}/persistentvolume/events,/clusters/{clusterName}/persistentvolume/storageclass/names,/clusters/{clusterName}/persistentvolume/yaml","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_pv"},"name":"新增","code":"skyview_pv_add","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list","create"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_pv"},"name":"编辑元数据","code":"skyview_pv_meta_data_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_pv"},"name":"编辑Yaml","code":"skyview_pv_yaml_edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_pv"},"name":"删除","code":"skyview_pv_remove","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list","delete"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_cluster_storage_service"},"name":"存储服务","code":"unified_platform_sys_cluster_storage_class","kind":"platform","url":"/cluster/space/scService/list","type":"menu","method":"get","annotation":{"resources":["namespaces","persistentvolumes","pods","resourcequotas","events","stellaris.harmonycloud.cn/clusters","apps/deployments","configmaps","services","secrets","persistentvolumeclaims","storage.k8s.io/storageclasses"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_storage_class"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/storageservices,/clusters/{clusterName}/storageservices/{storageServiceName},/clusters/{clusterName}/storageservices/{storageServiceName}/associatedresources","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_storage_class"},"name":"新增","code":"skyview_sc_service_add","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","create","update","patch"],"configmaps":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list","create","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"services":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","create"]}}},{"parent":{"code":"unified_platform_sys_cluster_storage_class"},"name":"扩容","code":"skyview_sc_service_expand","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"parent":{"code":"unified_platform_sys_cluster_storage_class"},"name":"删除","code":"skyview_sc_service_remove","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","delete"],"configmaps":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","delete"],"persistentvolumes":["get","list","delete"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list","delete"],"services":["get","list","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","delete"]}}}]}]},{"parent":{"code":"unified_platform_sys_cluster_manage_detail"},"name":"网络","code":"unified_platform_sys_cluster_network_manager","kind":"platform","icon":"network-menu","url":"","type":"menu","method":"get","children":[{"parent":{"code":"unified_platform_sys_cluster_network_manager"},"name":"Service服务","code":"unified_platform_sys_cluster_services","kind":"platform","url":"/cluster/space/network/service","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","services","expose.helper.harmonycloud.cn/layer4exposes","networking.k8s.io/ingresses","expose.helper.harmonycloud.cn/ingressclasses","configmaps","endpoints","apps/deployments","apps/statefulsets","apps/daemonsets","apps/replicasets","pods","nodes","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_services"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list"],"endpoints":["get","list"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_services"},"name":"新增","code":"skyview_service_add","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","create"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_services"},"name":"编辑","code":"skyview_service_edit","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_services"},"name":"新增四层对外路由","code":"skyview_service_add_four_layer_exponse","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_services"},"name":"新增七层对外路由","code":"skyview_service_add_seven_layer_exponse","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_services"},"name":"编辑对外路由","code":"edit_domains","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_services"},"name":"删除对外路由","code":"remove_domains","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch","delete"],"endpoints":["get","list","update","patch","delete"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","delete"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch","delete"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_services"},"name":"编辑元数据","code":"skyview_service_metadata_edit","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_services"},"name":"编辑yaml","code":"skyview_service_yaml_edit","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_services"},"name":"删除","code":"skyview_service_delete","kind":"platform","type":"permission","method":"delete","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch","delete"],"endpoints":["get","list","update","patch","delete"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","delete"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch","delete"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_cluster_network_manager"},"name":"Ingress路由","code":"unified_platform_sys_cluster_ingress","kind":"platform","url":"/cluster/space/network/ingress","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","configmaps","networking.k8s.io/ingresses","apps/daemonsets","apisix.apache.org/apisixroutes","resourcequotas","secrets","namespaces","apps/statefulsets","nodes","services","pods","events","apps/replicasets","endpoints","stellaris.harmonycloud.cn/multiclusterresources","expose.helper.harmonycloud.cn/ingressclasses","expose.helper.harmonycloud.cn/layer4exposes","apps/deployments","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_ingress"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list"],"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list"],"endpoints":["get","list"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list","watch"]}}},{"name":"新增 Nginx","code":"add_nginx","kind":"platform","type":"permission","method":"get","annotation":{}},{"name":"编辑Nginx","code":"edit_nginx","kind":"platform","type":"permission","method":"get","annotation":{}},{"name":"删除Nginx","code":"delete_nginx","kind":"platform","type":"permission","method":"get","annotation":{}},{"parent":{"code":"unified_platform_sys_cluster_ingress"},"name":"新增APISIX","code":"add_apisix","kind":"platform","type":"permission","method":"put","annotation":{}},{"parent":{"code":"unified_platform_sys_cluster_ingress"},"name":"编辑APISIX","code":"edit_apisix","kind":"platform","type":"permission","method":"put","annotation":{}},{"parent":{"code":"unified_platform_sys_cluster_ingress"},"name":"删除APISIX","code":"delete_apisix","kind":"platform","type":"permission","method":"put","annotation":{}},{"parent":{"code":"unified_platform_sys_cluster_ingress"},"name":"新增TLS","code":"add_tls","kind":"platform","type":"permission","method":"put","annotation":{}},{"parent":{"code":"unified_platform_sys_cluster_ingress"},"name":"编辑TLS","code":"edit_tls","kind":"platform","type":"permission","method":"put","annotation":{}},{"parent":{"code":"unified_platform_sys_cluster_ingress"},"name":"删除TLS","code":"delete_tls","kind":"platform","type":"permission","method":"put","annotation":{}},{"parent":{"code":"unified_platform_sys_cluster_ingress"},"name":"新增会话保持","code":"add_session","kind":"platform","type":"permission","method":"put","annotation":{}},{"parent":{"code":"unified_platform_sys_cluster_ingress"},"name":"编辑会话保持","code":"edit_session","kind":"platform","type":"permission","method":"put","annotation":{}},{"parent":{"code":"unified_platform_sys_cluster_ingress"},"name":"删除会话保持","code":"delete_session","kind":"platform","type":"permission","method":"put","annotation":{}}]},{"parent":{"code":"unified_platform_sys_cluster_network_manager"},"name":"网络域","code":"unified_platform_sys_cluster_network_area","kind":"platform","url":"/cluster/space/network/networkarea","type":"menu","method":"get","annotation":{"resources":["heimdallr.harmonycloud.cn/networkdetails","resourcequotas","mystra.heimdallr.harmonycloud.cn/podpolicies","isolate.harmonycloud.cn/hleases","heimdallr.harmonycloud.cn/hdsvcs","stellaris.harmonycloud.cn/multiclusterresources","events","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","stellaris.harmonycloud.cn/clusters","namespaces","pods","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdareas","configmaps","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_network_area"},"name":"查询","code":"query","kind":"platform","url":"/clusters/networkAreas","type":"permission","method":"get","annotation":{"component":"heimdallr","componentName":"统一网络模型","resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list","watch"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_cluster_network_manager"},"name":"网络IP池","code":"unified_platform_sys_cluster_network_ip_pool","kind":"platform","url":"/cluster/space/network/ippool","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/hdsvcs","isolate.harmonycloud.cn/hleases","mystra.heimdallr.harmonycloud.cn/podpolicies","configmaps","pods"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_network_ip_pool"},"name":"查询","code":"query","kind":"platform","url":"/clusters/networkIpPools","type":"permission","method":"get","annotation":{"component":"heimdallr","componentName":"统一网络模型","resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_cluster_network_manager"},"name":"负载均衡","code":"unified_platform_sys_cluster_network_loadbalance","kind":"platform","url":"/cluster/space/network/loadbalance","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","services","expose.helper.harmonycloud.cn/layer4exposes","networking.k8s.io/ingresses","expose.helper.harmonycloud.cn/ingressclasses","apisix.apache.org/apisixroutes","configmaps","secrets","endpoints","pods","nodes"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_network_loadbalance"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list"],"configmaps":["get","list"],"endpoints":["get","list"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_network_loadbalance"},"name":"新增","code":"add","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"services":["get","list","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_network_loadbalance"},"name":"编辑","code":"edit","kind":"platform","type":"permission","method":"put","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_network_loadbalance"},"name":"nginx配置","code":"edit_nginx","kind":"platform","type":"permission","method":"put","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch","delete"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_network_loadbalance"},"name":"删除","code":"remove","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch","delete"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch","delete"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch","delete"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch","delete"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch","delete"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]}]},{"parent":{"code":"unified_platform_sys_cluster_manage_detail"},"name":"Kubernetes资源","code":"unified_platform_sys_kubernetes_resource","kind":"platform","icon":"ip-pool-menu","url":"/cluster/space/kubernetesres","type":"menu","method":"get","children":[{"parent":{"code":"unified_platform_sys_kubernetes_resource"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_kubernetes_resource"},"name":"编辑Yaml","code":"skyview_cluster_resource_yaml_edit","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_kubernetes_resource"},"name":"删除","code":"skyview_cluster_resource_remove","kind":"platform","type":"permission","method":"get"}]},{"parent":{"code":"unified_platform_sys_cluster_manage_detail"},"name":"集群空间设置","code":"unified_platform_cluster_space_setting","kind":"platform","icon":"space-setting-menu","url":"","type":"menu","method":"get","children":[{"parent":{"code":"unified_platform_cluster_space_setting"},"name":"网络策略","code":"unified_platform_sys_cluster_network_podpolicy","kind":"platform","url":"/cluster/space/network/strategy","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/hdsvcs","isolate.harmonycloud.cn/hleases","mystra.heimdallr.harmonycloud.cn/podpolicies","configmaps","pods"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_network_podpolicy"},"name":"查询","code":"query","kind":"platform","url":"/clusters/{clusterName}/podPolicys/overview,/clusters/{clusterName}/namespaces/podPolicys","type":"permission","method":"get","annotation":{"component":"acl","componentName":"网络隔离","resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_network_podpolicy"},"name":"添加策略","code":"add","kind":"platform","type":"permission","method":"get","annotation":{"component":"acl","componentName":"网络隔离","resource_option":{"configmaps":["get","list","create","update","patch"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_network_podpolicy"},"name":"删除策略","code":"remove","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch","delete"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_network_podpolicy"},"name":"开启/关闭策略","code":"edit_state","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","create","update","patch","delete"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_network_podpolicy"},"name":"编辑策略","code":"edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","create","update","patch","delete"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_cluster_space_setting"},"name":"集群设置","code":"unified_platform_sys_cluster_setting","kind":"platform","url":"/cluster/space/clusterSetting","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","isolate.harmonycloud.cn/isolatelocks"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_setting"},"name":"集群隔离锁重置","code":"clusterIsolateLock","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"isolate.harmonycloud.cn/isolatelocks":["get","list","create","update","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_setting"},"name":"集群删除","code":"remove","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clusters":["get","list","delete"]}}}]},{"parent":{"code":"unified_platform_cluster_space_setting"},"name":"Kubeconfig","code":"unified_platform_sys_cluster_kubeconfig","kind":"platform","url":"/cluster/space/myCluster","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters"]},"children":[{"parent":{"code":"unified_platform_sys_cluster_kubeconfig"},"name":"查询","code":"query","kind":"platform","url":"/cluster/space/myCluster/kubeconfig","type":"permission","method":"get","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clusters":["watch","get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_kubeconfig"},"name":"下载","code":"cluster_kubeconfig_download","kind":"platform","url":"clusters/{clusterName}/kubeconfig/download","type":"permission","method":"get","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clusters":["watch","get","list"]}}}]}]}]},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"编辑标签","code":"editLabel","kind":"platform","url":"/clusters/{clusterName}/labels","type":"permission","method":"put","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clusters":["get","list","create","update","patch"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"编辑描述","code":"editDescribe","kind":"platform","url":"/clusters/{clusterName}/description","type":"permission","method":"put","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clusters":["get","list","create","update","patch"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"纳管集群","code":"add","kind":"platform","url":"/clusters/token","type":"permission","method":"get","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clusters":["get","list","create","update","patch"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"删除","code":"remove","kind":"platform","url":"/clusters/{clusterName}","type":"permission","method":"delete","annotation":{"resource_option":{"installer.unified-platform.harmonycloud.cn/installers":["watch","get","list","create","update","patch","delete"],"stellaris.harmonycloud.cn/clusters":["watch","get","list","create","update","patch","delete"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"查询","code":"query","kind":"platform","url":"/apis/v1/clusters-create/{clusterName}","type":"permission","method":"get","annotation":{"resource_option":{"installer.unified-platform.harmonycloud.cn/installers":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"创建集群","code":"create-cluster","kind":"platform","url":"/apis/v1/clusters-create","type":"permission","method":"get","annotation":{"resource_option":{"installer.unified-platform.harmonycloud.cn/installers":["get","list","create"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"编辑集群","code":"edit-cluster","kind":"platform","url":"/apis/v1/clusters-create/{clusterName}","type":"permission","method":"get","annotation":{"resource_option":{"installer.unified-platform.harmonycloud.cn/installers":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list","update","patch"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"集群组管理","code":"clusterTopologies","kind":"platform","url":"/clusters/clusterTopologies","type":"permission","method":"get","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"集群组新增","code":"clusterTopologies_add","kind":"platform","url":"/clusters/clusterTopologies/{clusterTopologiesName}","type":"permission","method":"post","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clustertopologies":["get","list","create"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"集群组编辑","code":"clusterTopologies_edit","kind":"platform","url":"/clusters/clusterTopologies/{clusterTopologiesName}","type":"permission","method":"put","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clustertopologies":["get","list","update","patch"],"stellaris.harmonycloud.cn/servicesyncs":["get","list"],"namespaces":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"集群组删除","code":"clusterTopologies_delete","kind":"platform","url":"/clusters/clusterTopologies/{clusterTopologiesName}","type":"permission","method":"delete","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clustertopologies":["get","list","delete"],"stellaris.harmonycloud.cn/servicesyncs":["get","list"],"namespaces":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_cluster_manage"},"name":"集群组分配项目","code":"clusterTopologies_projects","kind":"platform","url":"/clusters/clusterTopologies/{clusterTopologiesName}/organ","type":"permission","method":"put","annotation":{"resource_option":{"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/servicesyncs":["get","list"],"namespaces":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_platform_mgr"},"name":"资源池","code":"unified_platform_sys_node_pool","kind":"platform","icon":"v35_ResourcePool","url":"/quotacenter/poolList","type":"menu","method":"get","annotation":{"resources":["nodes","stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","isolate.harmonycloud.cn/isolatelocks","isolate.harmonycloud.cn/hleases","harmonycloud.cn/nodepools"]},"children":[{"parent":{"code":"unified_platform_sys_node_pool"},"name":"查询","code":"query","kind":"platform","url":"/clusters/nodepools,/clusters/nodepools/filters","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_pool"},"name":"新增","code":"add","kind":"platform","url":"/clusters/{clusterName}/nodepools","type":"permission","method":"post","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_pool"},"name":"主机管理","code":"node_manage","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","delete","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_pool"},"name":"添加主机","code":"add_node","kind":"platform","url":"/clusters/{clusterName}/nodepools/{nodePoolName}/nodes","type":"permission","method":"put","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_pool"},"name":"移除主机","code":"remove_node","kind":"platform","url":"/clusters/{clusterName}/nodepools/{nodePoolName}/nodes/{nodeName}","type":"permission","method":"delete","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_pool"},"name":"分配组织","code":"assign_to_org","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_pool"},"name":"添加组织配额","code":"add_quota","kind":"platform","url":"/clusters/{clusterName}/nodepools/{nodePoolName}/organizations/{organizationId}/quota","type":"permission","method":"post","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_pool"},"name":"编辑组织配额","code":"edit_quota","kind":"platform","url":"/clusters/{clusterName}/nodepools/{nodePoolName}/organizations/{organizationId}/quota","type":"permission","method":"post","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_pool"},"name":"删除组织配额","code":"remove_quota","kind":"platform","url":"/clusters/{clusterName}/nodepools/{nodePoolName}/organizations/{organizationId}/quota","type":"permission","method":"delete","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_node_pool"},"name":"删除","code":"remove","kind":"platform","url":"/clusters/{clusterName}/nodepools/{nodePoolName}","type":"permission","method":"delete","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch","delete"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_platform_mgr"},"name":"存储服务","code":"unified_platform_sys_storage","kind":"platform","icon":"v35_StorageClass","url":"/quotacenter/storageList","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","secrets","apps/deployments","events","persistentvolumeclaims","services","pods","resourcequotas","namespaces","persistentvolumes","storage.k8s.io/storageclasses","configmaps"]},"children":[{"parent":{"code":"unified_platform_sys_storage"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"parent":{"code":"unified_platform_sys_storage"},"name":"新增","code":"add","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/deployments":["get","list","create","update","patch"],"configmaps":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list","create","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"services":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","create"]}}},{"parent":{"code":"unified_platform_sys_storage"},"name":"扩容","code":"capacity_expansion","kind":"platform","type":"permission","method":"put","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"parent":{"code":"unified_platform_sys_storage"},"name":"分配组织","code":"assign_to_org","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"parent":{"code":"unified_platform_sys_storage"},"name":"添加组织配额","code":"add_quota","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"parent":{"code":"unified_platform_sys_storage"},"name":"编辑组织配额","code":"edit_quota","kind":"platform","type":"permission","method":"put","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"parent":{"code":"unified_platform_sys_storage"},"name":"删除组织配额","code":"remove_quota","kind":"platform","type":"permission","method":"delete","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"parent":{"code":"unified_platform_sys_storage"},"name":"删除","code":"remove","kind":"platform","type":"permission","method":"delete","annotation":{"resource_option":{"apps/deployments":["get","list","delete"],"configmaps":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","delete"],"persistentvolumes":["get","list","delete"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list","delete"],"services":["get","list","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","delete"]}}}]},{"parent":{"code":"unified_platform_sys_platform_mgr"},"name":"网络管理","code":"unified_platform_sys_network_manage","kind":"platform","icon":"v35_Network","url":"","type":"menu","method":"get","annotation":{"ceiling":true,"titleDescription":"双栈及IP网络规划管理"},"children":[{"parent":{"code":"unified_platform_sys_network_manage"},"name":"网络规划","code":"unified_platform_sys_network_resource_manager","kind":"platform","icon":"network-plan-menu","url":"/networkmanage/resource","type":"menu","method":"get","annotation":{"resources":["resourcequotas","stellaris.harmonycloud.cn/clusters","events","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/networkresources","namespaces"]},"children":[{"parent":{"code":"unified_platform_sys_network_resource_manager"},"name":"查询","code":"skyview_cluster_resource_yaml_get","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_resource_manager"},"name":"编辑","code":"edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","create","update","patch"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_network_manage"},"name":"网络域","code":"unified_platform_sys_network_area","kind":"platform","icon":"network-domain-menu","url":"/quotacenter/networkarea","type":"menu","method":"get","annotation":{"resources":["configmaps","heimdallr.harmonycloud.cn/hdareas","stellaris.harmonycloud.cn/multiclusterresourcebindings","stellaris.harmonycloud.cn/clusters","pods","heimdallr.harmonycloud.cn/hdsvcs","events","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/hdpools","isolate.harmonycloud.cn/hleases","heimdallr.harmonycloud.cn/networkdetails","namespaces","resourcequotas","stellaris.harmonycloud.cn/multiclusterresources","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","mystra.heimdallr.harmonycloud.cn/podpolicies"]},"children":[{"parent":{"code":"unified_platform_sys_network_area"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_area"},"name":"新增","code":"add","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_area"},"name":"编辑","code":"edit","kind":"platform","type":"permission","method":"put","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_area"},"name":"分配组织","code":"assign_to_org","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_area"},"name":"全局共享","code":"global_sharing","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_area"},"name":"删除","code":"remove","kind":"platform","type":"permission","method":"delete","annotation":{"resource_option":{"configmaps":["get","list","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete"],"isolate.harmonycloud.cn/hleases":["get","list","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_network_manage"},"name":"网络IP池","code":"unified_platform_sys_network_area_ip_pool","kind":"platform","icon":"ip-pool-menu","url":"/quotacenter/ippool","type":"menu","method":"get","annotation":{"resources":["mystra.heimdallr.harmonycloud.cn/podpolicies","heimdallr.harmonycloud.cn/networkdetails","resourcequotas","isolate.harmonycloud.cn/hleases","heimdallr.harmonycloud.cn/hdpools","stellaris.harmonycloud.cn/multiclusterresources","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdsvcs","pods","events","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/hdareas","namespaces","stellaris.harmonycloud.cn/multiclusterresourcebindings","stellaris.harmonycloud.cn/clusters","heimdallr.harmonycloud.cn/hdblocks","configmaps"]},"children":[{"parent":{"code":"unified_platform_sys_network_area_ip_pool"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_area_ip_pool"},"name":"新增","code":"add","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_area_ip_pool"},"name":"编辑","code":"edit","kind":"platform","type":"permission","method":"put","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_area_ip_pool"},"name":"分配组织","code":"assign_to_org","kind":"platform","type":"permission","method":"put","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_area_ip_pool"},"name":"全局共享","code":"global_sharing","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_area_ip_pool"},"name":"删除","code":"remove","kind":"platform","type":"permission","method":"delete","annotation":{"resource_option":{"configmaps":["get","list","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete"],"isolate.harmonycloud.cn/hleases":["get","list","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_network_manage"},"name":"虚拟机网络","code":"unified_platform_sys_vm_network","kind":"platform","url":"/networkmanage/virtualmachine","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","kubeovn.io/vlans","kubeovn.io/subnets","kubeovn.io/ips","kubevirt.io/virtualmachines"]},"children":[{"parent":{"code":"unified_platform_sys_vm_network"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list"],"kubeovn.io/vlans":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_vm_network"},"name":"编辑描述","code":"virtual_description_edit","kind":"platform","type":"permission","method":"put","annotation":{"resource_option":{"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list","update"],"kubeovn.io/vlans":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_vm_network"},"name":"新增子网","code":"virtual_network_add","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list","create"],"kubeovn.io/vlans":["get","list","create"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_vm_network"},"name":"删除子网","code":"virtual_network_delete","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list","delete"],"kubeovn.io/vlans":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_vm_network"},"name":"分配组织","code":"virtual_network_assign_to_org","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list","update"],"kubeovn.io/vlans":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_vm_network"},"name":"全局共享","code":"virtual_network_global_sharing","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list","update"],"kubeovn.io/vlans":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_network_manage"},"name":"网络策略","code":"unified_platform_sys_network_policy","kind":"platform","url":"/networkmanage/space/strategy","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/hdsvcs","isolate.harmonycloud.cn/hleases","mystra.heimdallr.harmonycloud.cn/podpolicies","configmaps","pods"]},"children":[{"parent":{"code":"unified_platform_sys_network_policy"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_policy"},"name":"添加策略","code":"add","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_policy"},"name":"删除策略","code":"remove","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete"],"isolate.harmonycloud.cn/hleases":["get","list","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_policy"},"name":"开启/关闭策略","code":"edit_state","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_policy"},"name":"编辑策略","code":"edit","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_network_manage"},"name":"网络模板","code":"unified_platform_sys_network_template","kind":"platform","url":"/networkmanage/space/template","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/hdsvcs","isolate.harmonycloud.cn/hleases","mystra.heimdallr.harmonycloud.cn/podpolicies","configmaps","pods"]},"children":[{"parent":{"code":"unified_platform_sys_network_template"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_template"},"name":"新增","code":"skyview_network_space_template_add","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_template"},"name":"编辑","code":"skyview_network_space_template_update","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_template"},"name":"删除","code":"skyview_network_space_template_remove","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete"],"isolate.harmonycloud.cn/hleases":["get","list","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_network_manage"},"name":"网络配置","code":"unified_platform_sys_network_setting","kind":"platform","url":"/networkmanage/space/setting","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/hdsvcs","isolate.harmonycloud.cn/hleases","mystra.heimdallr.harmonycloud.cn/podpolicies","configmaps","pods"]},"children":[{"parent":{"code":"unified_platform_sys_network_setting"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_network_setting"},"name":"IP使用配置","code":"network_ip_use_config","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_network_manage"},"name":"证书管理","code":"unified_platform_sys_certificate_manager","kind":"platform","url":"/quotacenter/certificate","type":"menu","method":"get","annotation":{},"children":[{"parent":{"code":"unified_platform_sys_certificate_manager"},"name":"新增","code":"skyview_certificate_add","kind":"platform","type":"permission","method":"get","annotation":{}},{"parent":{"code":"unified_platform_sys_certificate_manager"},"name":"编辑","code":"skyview_certificate_edit","kind":"platform","type":"permission","method":"get","annotation":{}},{"parent":{"code":"unified_platform_sys_certificate_manager"},"name":"分配租户","code":"skyview_certificate_assign","kind":"platform","type":"permission","method":"get","annotation":{}},{"parent":{"code":"unified_platform_sys_certificate_manager"},"name":"删除","code":"skyview_certificate_delete","kind":"platform","type":"permission","method":"get","annotation":{}}]}]},{"parent":{"code":"unified_platform_sys_platform_mgr"},"name":"负载均衡","code":"unified_platform_sys_loadbalance","kind":"platform","icon":"v35_LoadBalance","url":"/quotacenter/loadbalance","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","services","expose.helper.harmonycloud.cn/layer4exposes","networking.k8s.io/ingresses","expose.helper.harmonycloud.cn/ingressclasses","apisix.apache.org/apisixroutes","configmaps","secrets","endpoints","pods","nodes"]},"children":[{"parent":{"code":"unified_platform_sys_loadbalance"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list"],"configmaps":["get","list"],"endpoints":["get","list"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_loadbalance"},"name":"新增","code":"add","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"services":["get","list","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_loadbalance"},"name":"编辑","code":"edit","kind":"platform","type":"permission","method":"put","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_loadbalance"},"name":"分配组织","code":"assign_to_org","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_loadbalance"},"name":"全局共享","code":"global_sharing","kind":"platform","type":"permission","method":"delete","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_loadbalance"},"name":"nginx配置","code":"edit_nginx","kind":"platform","type":"permission","method":"put","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch","delete"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"parent":{"code":"unified_platform_sys_loadbalance"},"name":"删除","code":"remove","kind":"platform","type":"permission","method":"post","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch","delete"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch","delete"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch","delete"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch","delete"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch","delete"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"parent":{"code":"unified_platform_sys_platform_mgr"},"name":"制品服务","code":"unified_platform_sys_registry","kind":"platform","icon":"v35_Artifacts","url":"/quotacenter/registry","type":"menu","method":"get","children":[{"parent":{"code":"unified_platform_sys_registry"},"name":"查询","code":"query","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"新增","code":"add","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"移除","code":"remove","kind":"platform","type":"permission","method":"delete"},{"parent":{"code":"unified_platform_sys_registry"},"name":"新增仓库","code":"add_repos","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"分配组织","code":"assign_to_org","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"删除仓库","code":"remove_repos","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"新增备份规则","code":"repo_add_rule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"编辑备份规则","code":"repo_edit_rule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"执行备份规则","code":"repo_execute_rule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"删除备份规则","code":"repo_remove_rule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"新增备份服务器","code":"add_server","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"编辑备份服务器","code":"edit_server","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"删除备份服务器","code":"remove_server","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"编辑垃圾清理","code":"edit_schedule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"立即清理垃圾","code":"execute_schedule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"编辑Harbor配置","code":"edit_harbor_config","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"编辑仓库标签","code":"repo_label_manage","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"上传","code":"upload_image","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"删除镜像","code":"remove_image","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"查看版本","code":"view_version","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"添加规则","code":"add_rule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"立即执行","code":"execute_rule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"编辑定时任务","code":"edit_timed_task","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"模拟运行","code":"simulation_run","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"中止","code":"suspend_rule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"日志","code":"log","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"编辑规则","code":"edit_rule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"启用/禁用规则","code":"enable_disable_rule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"删除规则","code":"remove_rule","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"内容信任","code":"content_trust","kind":"platform","type":"permission","method":"post"},{"parent":{"code":"unified_platform_sys_registry"},"name":"阻止潜在漏洞镜像","code":"Block_potential_vulnerability_mirroring","kind":"platform","type":"permission","method":"post"},{"parent":{"code":"unified_platform_sys_registry"},"name":"自动扫描镜像","code":"auto_scan","kind":"platform","type":"permission","method":"post"},{"parent":{"code":"unified_platform_sys_registry"},"name":"编辑CVE白名单","code":"CVE_whitelist","kind":"platform","type":"permission","method":"post"},{"parent":{"code":"unified_platform_sys_registry"},"name":"镜像复制","code":"copy","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"构建记录","code":"build_record","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"漏洞扫描","code":"bug_scan","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"拉取/下载","code":"download","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"标签管理","code":"label_manage","kind":"platform","type":"permission","method":"get"},{"parent":{"code":"unified_platform_sys_registry"},"name":"删除镜像版本","code":"images_remove","kind":"platform","type":"permission","method":"get"}]},{"parent":{"code":"unified_platform_sys_platform_mgr"},"name":"数据中心","code":"unified_platform_sys_datacenter","kind":"platform","icon":"v35_Datacenter","url":"/logicalDatacenter","type":"menu","method":"get","children":[{"name":"创建数据中心","code":"add_center","kind":"platform","type":"permission","method":"post"},{"name":"编辑数据中心","code":"edit_center","kind":"platform","type":"permission","method":"put"},{"name":"删除数据中心","code":"delete_center","kind":"platform","type":"permission","method":"delete"},{"name":"创建逻辑单元","code":"add_unit","kind":"platform","type":"permission","method":"post"},{"name":"编辑逻辑单元","code":"edit_unit","kind":"platform","type":"permission","method":"put"},{"name":"删除逻辑单元","code":"delete_unit","kind":"platform","type":"permission","method":"delete"},{"name":"绑定集群","code":"bind_cluster","kind":"platform","type":"permission","method":"put"},{"name":"绑定备份服务器","code":"bind_storage_server","kind":"platform","type":"permission","method":"put"},{"name":"绑定制品服务","code":"bind_registry","kind":"platform","type":"permission","method":"put"}]},{"parent":{"code":"unified_platform_sys_platform_mgr"},"name":"基线合规","code":"unified_platform_baseline","kind":"platform","icon":"v35_Network","type":"menu","method":"get","children":[{"name":"检查策略","code":"unified_platform_baseline_strategy","kind":"platform","url":"/baseline/checkpolicy","type":"menu","method":"get","children":[{"name":"新增","code":"unified_platform_baseline_strategy_create","kind":"platform","type":"permission","method":"post"},{"name":"编辑","code":"unified_platform_baseline_strategy_edit","kind":"platform","type":"permission","method":"post"},{"name":"删除","code":"unified_platform_baseline_strategy_delete","kind":"platform","type":"permission","method":"post"},{"name":"开启策略","code":"unified_platform_baseline_strategy_enabled","kind":"platform","type":"permission","method":"post"},{"name":"执行策略检查","code":"unified_platform_baseline_strategy_execute_check","kind":"platform","type":"permission","method":"post"}]},{"name":"基线标准","code":"unified_platform_baseline_standard","kind":"platform","url":"/baseline/standard","type":"menu","method":"get","children":[{"name":"新增基线分类","code":"unified_platform_baseline_standard_category_create","kind":"platform","type":"permission","method":"post"},{"name":"编辑基线分类","code":"unified_platform_baseline_standard_category_edit","kind":"platform","type":"permission","method":"post"},{"name":"删除基线分类","code":"unified_platform_baseline_standard_category_delete","kind":"platform","type":"permission","method":"post"},{"name":"新增基线标准","code":"unified_platform_baseline_standard_create","kind":"platform","type":"permission","method":"post"},{"name":"编辑基线标准","code":"unified_platform_baseline_standard_edit","kind":"platform","type":"permission","method":"post"},{"name":"删除基线标准","code":"unified_platform_baseline_standard_delete","kind":"platform","type":"permission","method":"post"}]},{"name":"自定义检查项","code":"unified_platform_baseline_check_item","kind":"platform","url":"/baseline/checkitem","type":"menu","method":"get","children":[{"name":"新增","code":"unified_platform_baseline_check_item_create","kind":"platform","type":"permission","method":"post"},{"name":"编辑","code":"unified_platform_baseline_check_item_edit","kind":"platform","type":"permission","method":"post"},{"name":"删除","code":"unified_platform_baseline_check_item_delete","kind":"platform","type":"permission","method":"post"}]}]}]}]