apiVersion: cloud-service.harmonycloud.cn/v1alpha1
kind: Material
metadata:
  name: olympus-unified-platform-2
  annotations:
    message.material.harmonycloud.cn/reapply: "true"
spec:
  cloudServiceName: unified-platform
  waitPolicy:
    onSuccess:
      - name: olympus-unified-platform-1
  tasks:
    - name: unified-platform-application
      kind: permissionImport
      permissionConfig:
        app:
          code: application
        permissions: |-
          [{
          	"name": "门户概览",
          	"code": "unified_platform_sys_overview",
          	"kind": "platform",
          	"icon": "v35_PortalOverview",
          	"url": "/quotacenter/space/overview",
          	"type": "menu",
          	"method": "get",
          	"parent": {
          		"code": "platform",
          		"cloudServiceName": "unified-platform"
          	},
          	"sort": 1,
          	"annotation": {
          		"resources": ["stellaris.harmonycloud.cn/clustersets", "crd.projectcalico.org/hostendpoints", "core.oam.dev/workloaddefinitions", "crd.projectcalico.org/clusterinformations", "replicationcontrollers", "componentstatuses", "batch/cronjobs", "mysql.middleware.harmonycloud.cn/mysqlreplicates", "storage.k8s.io/storageclasses", "crd.projectcalico.org/felixconfigurations", "rbac.authorization.k8s.io/rolebindings", "podtemplates", "configmaps", "batch/jobs", "redis.middleware.hc.cn/redisclusters", "mystra.heimdallr.harmonycloud.cn/identities", "keda.sh/scaledjobs", "secrets", "heimdallr.harmonycloud.cn/hdsvcs", "core.oam.dev/componentdefinitions", "persistentvolumeclaims", "apps/controllerrevisions", "crd.projectcalico.org/globalnetworkpolicies", "stellaris.harmonycloud.cn/multiclusterhorizontalpodautoscalers", "snapshot.storage.k8s.io/volumesnapshotclasses", "core.oam.dev/applications", "core.oam.dev/workflowstepdefinitions", "monitoring.coreos.com/prometheusrules", "policy/poddisruptionbudgets", "harmonycloud.cn/nodepools", "limitranges", "networking.k8s.io/ingresses", "monitoring.coreos.com/prometheuses", "rollouts.kruise.io/rollouts", "autoscaling.alibabacloud.com/cronhorizontalpodautoscalers", "scheduling.k8s.io/priorityclasses", "events.k8s.io/events", "crd.projectcalico.org/networksets", "crd.projectcalico.org/ipamblocks", "discovery.k8s.io/endpointslices", "core.oam.dev/policydefinitions", "core.oam.dev/applicationrevisions", "crd.projectcalico.org/ipamconfigs", "admissionregistration.k8s.io/mutatingwebhookconfigurations", "stellaris.harmonycloud.cn/multiclusterresourceschedulepolicies", "crd.projectcalico.org/blockaffinities", "stellaris.harmonycloud.cn/resourceaggregatepolicies", "namespaces", "crd.projectcalico.org/kubecontrollersconfigurations", "heimdallr.harmonycloud.cn/networkresources", "standard.oam.dev/rollouts", "mysql.middleware.harmonycloud.cn/mysqlbackups", "apps/replicasets", "harmonycloud.cn/podsecuritypolicytemplates", "monitoring.coreos.com/podmonitors", "rbac.authorization.k8s.io/clusterrolebindings", "stellaris.harmonycloud.cn/multiclusterresourceaggregatepolicies", "core.oam.dev/workflows", "monitoring.coreos.com/servicemonitors", "snapshot.storage.k8s.io/volumesnapshots", "storage.k8s.io/csidrivers", "certificates.k8s.io/certificatesigningrequests", "crd.projectcalico.org/networkpolicies", "crd.projectcalico.org/ipreservations", "apiregistration.k8s.io/apiservices", "es.middleware.hc.cn/esclusters", "crd.projectcalico.org/ipamhandles", "events", "isolate.harmonycloud.cn/hleases", "webhook.harmonycloud.cn/isolationwebhookconfigurations", "networking.k8s.io/ingressclasses", "nodes", "apps/deployments", "expose.helper.harmonycloud.cn/layer4exposes", "rollouts.kruise.io/batchreleases", "monitoring.coreos.com/alertmanagers", "keda.sh/scaledobjects", "rocketmq.middleware.hc.cn/brokerclusters", "apiextensions.k8s.io/customresourcedefinitions", "admissionregistration.k8s.io/validatingwebhookconfigurations", "crd.projectcalico.org/ippools", "heimdallr.harmonycloud.cn/hdblocks", "cluster.core.oam.dev/clustergateways", "networking.k8s.io/networkpolicies", "apps/daemonsets", "mysql.middleware.harmonycloud.cn/mysqlclusters", "coordination.k8s.io/leases", "persistentvolumes", "isolate.harmonycloud.cn/isolatelocks", "application.decompile.harmonycloud.cn/decompileconfigs", "stellaris.harmonycloud.cn/multiclusterresourceaggregaterules", "stellaris.harmonycloud.cn/clusters", "resourcequotas", "core.oam.dev/traitdefinitions", "mysql.middleware.harmonycloud.cn/mysqlbackupschedules", "snapshot.storage.k8s.io/volumesnapshotcontents", "heimdallr.harmonycloud.cn/hdareas", "serviceaccounts", "mystra.heimdallr.harmonycloud.cn/podpolicies", "rbac.authorization.k8s.io/clusterroles", "flowcontrol.apiserver.k8s.io/prioritylevelconfigurations", "bifrost.heimdallr.harmonycloud.cn/subnets", "metrics.k8s.io/nodes", "expose.helper.harmonycloud.cn/ingressclasses", "heimdallr.harmonycloud.cn/networkdetails", "extensions/ingresses", "storage.k8s.io/csinodes", "stellaris.harmonycloud.cn/namespacemappings", "crd.projectcalico.org/caliconodestatuses", "services", "crd.projectcalico.org/globalnetworksets", "bindings", "crd.projectcalico.org/bgpconfigurations", "stellaris.harmonycloud.cn/clusterresources", "flowcontrol.apiserver.k8s.io/flowschemas", "keda.sh/clustertriggerauthentications", "heimdallr.harmonycloud.cn/hdpods", "metrics.k8s.io/pods", "endpoints", "heimdallr.harmonycloud.cn/hdpools", "rbac.authorization.k8s.io/roles", "storage.k8s.io/volumeattachments", "crd.projectcalico.org/bgppeers", "keda.sh/triggerauthentications", "core.oam.dev/scopedefinitions", "k8s.cni.cncf.io/network-attachment-definitions", "stellaris.harmonycloud.cn/multiclusterresourcebindings", "heimdallr.harmonycloud.cn/iplocks", "mystra.heimdallr.harmonycloud.cn/clusternetworkpolicies", "stellaris.harmonycloud.cn/aggregatedresources", "apps/statefulsets", "autoscaling/horizontalpodautoscalers", "stellaris.harmonycloud.cn/multiclusterresources", "core.oam.dev/resourcetrackers", "pods"]
          	},
          	"children": [{
          		"name": "查询",
          		"code": "query",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get",
          		"annotation": {
          			"resource_option": {
          				"admissionregistration.k8s.io/mutatingwebhookconfigurations": ["get", "list"],
          				"admissionregistration.k8s.io/validatingwebhookconfigurations": ["get", "list"],
          				"apiextensions.k8s.io/customresourcedefinitions": ["get", "list"],
          				"apiregistration.k8s.io/apiservices": ["get", "list"],
          				"application.decompile.harmonycloud.cn/decompileconfigs": ["get", "list"],
          				"apps/controllerrevisions": ["get", "list"],
          				"apps/daemonsets": ["get", "list"],
          				"apps/deployments": ["get", "list"],
          				"apps/replicasets": ["get", "list"],
          				"apps/statefulsets": ["get", "list"],
          				"autoscaling.alibabacloud.com/cronhorizontalpodautoscalers": ["get", "list"],
          				"autoscaling/horizontalpodautoscalers": ["get", "list"],
          				"batch/cronjobs": ["get", "list"],
          				"batch/jobs": ["get", "list"],
          				"bifrost.heimdallr.harmonycloud.cn/subnets": ["get", "list"],
          				"bindings": ["get", "list"],
          				"certificates.k8s.io/certificatesigningrequests": ["get", "list"],
          				"cluster.core.oam.dev/clustergateways": ["get", "list"],
          				"componentstatuses": ["get", "list"],
          				"configmaps": ["get", "list"],
          				"coordination.k8s.io/leases": ["get", "list"],
          				"core.oam.dev/applicationrevisions": ["get", "list"],
          				"core.oam.dev/applications": ["get", "list"],
          				"core.oam.dev/componentdefinitions": ["get", "list"],
          				"core.oam.dev/policydefinitions": ["get", "list"],
          				"core.oam.dev/resourcetrackers": ["get", "list"],
          				"core.oam.dev/scopedefinitions": ["get", "list"],
          				"core.oam.dev/traitdefinitions": ["get", "list"],
          				"core.oam.dev/workflows": ["get", "list"],
          				"core.oam.dev/workflowstepdefinitions": ["get", "list"],
          				"core.oam.dev/workloaddefinitions": ["get", "list"],
          				"crd.projectcalico.org/bgpconfigurations": ["get", "list"],
          				"crd.projectcalico.org/bgppeers": ["get", "list"],
          				"crd.projectcalico.org/blockaffinities": ["get", "list"],
          				"crd.projectcalico.org/caliconodestatuses": ["get", "list"],
          				"crd.projectcalico.org/clusterinformations": ["get", "list"],
          				"crd.projectcalico.org/felixconfigurations": ["get", "list"],
          				"crd.projectcalico.org/globalnetworkpolicies": ["get", "list"],
          				"crd.projectcalico.org/globalnetworksets": ["get", "list"],
          				"crd.projectcalico.org/hostendpoints": ["get", "list"],
          				"crd.projectcalico.org/ipamblocks": ["get", "list"],
          				"crd.projectcalico.org/ipamconfigs": ["get", "list"],
          				"crd.projectcalico.org/ipamhandles": ["get", "list"],
          				"crd.projectcalico.org/ippools": ["get", "list"],
          				"crd.projectcalico.org/ipreservations": ["get", "list"],
          				"crd.projectcalico.org/kubecontrollersconfigurations": ["get", "list"],
          				"crd.projectcalico.org/networkpolicies": ["get", "list"],
          				"crd.projectcalico.org/networksets": ["get", "list"],
          				"discovery.k8s.io/endpointslices": ["get", "list"],
          				"endpoints": ["get", "list"],
          				"es.middleware.hc.cn/esclusters": ["get", "list"],
          				"events": ["get", "list"],
          				"events.k8s.io/events": ["get", "list"],
          				"expose.helper.harmonycloud.cn/ingressclasses": ["get", "list"],
          				"expose.helper.harmonycloud.cn/layer4exposes": ["get", "list"],
          				"extensions/ingresses": ["get", "list"],
          				"flowcontrol.apiserver.k8s.io/flowschemas": ["get", "list"],
          				"flowcontrol.apiserver.k8s.io/prioritylevelconfigurations": ["get", "list"],
          				"harmonycloud.cn/nodepools": ["get", "list", "list"],
          				"harmonycloud.cn/podsecuritypolicytemplates": ["get", "list"],
          				"heimdallr.harmonycloud.cn/hdareas": ["get", "list"],
          				"heimdallr.harmonycloud.cn/hdblocks": ["get", "list"],
          				"heimdallr.harmonycloud.cn/hdpods": ["get", "list"],
          				"heimdallr.harmonycloud.cn/hdpools": ["get", "list"],
          				"heimdallr.harmonycloud.cn/hdsvcs": ["get", "list"],
          				"heimdallr.harmonycloud.cn/iplocks": ["get", "list"],
          				"heimdallr.harmonycloud.cn/networkdetails": ["get", "list"],
          				"heimdallr.harmonycloud.cn/networkresources": ["get", "list"],
          				"isolate.harmonycloud.cn/hleases": ["get", "list"],
          				"isolate.harmonycloud.cn/isolatelocks": ["get", "list"],
          				"k8s.cni.cncf.io/network-attachment-definitions": ["get", "list"],
          				"keda.sh/clustertriggerauthentications": ["get", "list"],
          				"keda.sh/scaledjobs": ["get", "list"],
          				"keda.sh/scaledobjects": ["get", "list"],
          				"keda.sh/triggerauthentications": ["get", "list"],
          				"limitranges": ["get", "list"],
          				"metrics.k8s.io/nodes": ["get", "list"],
          				"metrics.k8s.io/pods": ["get", "list"],
          				"monitoring.coreos.com/alertmanagers": ["get", "list"],
          				"monitoring.coreos.com/podmonitors": ["get", "list"],
          				"monitoring.coreos.com/prometheuses": ["get", "list"],
          				"monitoring.coreos.com/prometheusrules": ["get", "list"],
          				"monitoring.coreos.com/servicemonitors": ["get", "list"],
          				"mysql.middleware.harmonycloud.cn/mysqlbackups": ["get", "list"],
          				"mysql.middleware.harmonycloud.cn/mysqlbackupschedules": ["get", "list"],
          				"mysql.middleware.harmonycloud.cn/mysqlclusters": ["get", "list"],
          				"mysql.middleware.harmonycloud.cn/mysqlreplicates": ["get", "list"],
          				"mystra.heimdallr.harmonycloud.cn/clusternetworkpolicies": ["get", "list"],
          				"mystra.heimdallr.harmonycloud.cn/identities": ["get", "list"],
          				"mystra.heimdallr.harmonycloud.cn/podpolicies": ["get", "list"],
          				"namespaces": ["get", "list"],
          				"networking.k8s.io/ingressclasses": ["get", "list"],
          				"networking.k8s.io/ingresses": ["get", "list"],
          				"networking.k8s.io/networkpolicies": ["get", "list"],
          				"nodes": ["get", "list"],
          				"persistentvolumeclaims": ["get", "list"],
          				"persistentvolumes": ["get", "list"],
          				"pods": ["get", "list"],
          				"podtemplates": ["get", "list"],
          				"policy/poddisruptionbudgets": ["get", "list"],
          				"rbac.authorization.k8s.io/clusterrolebindings": ["get", "list"],
          				"rbac.authorization.k8s.io/clusterroles": ["get", "list"],
          				"rbac.authorization.k8s.io/rolebindings": ["get", "list"],
          				"rbac.authorization.k8s.io/roles": ["get", "list"],
          				"redis.middleware.hc.cn/redisclusters": ["get", "list"],
          				"replicationcontrollers": ["get", "list"],
          				"resourcequotas": ["get", "list"],
          				"rocketmq.middleware.hc.cn/brokerclusters": ["get", "list"],
          				"rollouts.kruise.io/batchreleases": ["get", "list"],
          				"rollouts.kruise.io/rollouts": ["get", "list"],
          				"scheduling.k8s.io/priorityclasses": ["get", "list"],
          				"secrets": ["get", "list"],
          				"serviceaccounts": ["get", "list"],
          				"services": ["get", "list"],
          				"snapshot.storage.k8s.io/volumesnapshotclasses": ["get", "list"],
          				"snapshot.storage.k8s.io/volumesnapshotcontents": ["get", "list"],
          				"snapshot.storage.k8s.io/volumesnapshots": ["get", "list"],
          				"standard.oam.dev/rollouts": ["get", "list"],
          				"stellaris.harmonycloud.cn/aggregatedresources": ["get", "list"],
          				"stellaris.harmonycloud.cn/clusterresources": ["get", "list"],
          				"stellaris.harmonycloud.cn/clusters": ["watch", "get", "list", "list"],
          				"stellaris.harmonycloud.cn/clustersets": ["get", "list"],
          				"stellaris.harmonycloud.cn/multiclusterhorizontalpodautoscalers": ["get", "list"],
          				"stellaris.harmonycloud.cn/multiclusterresourceaggregatepolicies": ["get", "list"],
          				"stellaris.harmonycloud.cn/multiclusterresourceaggregaterules": ["get", "list"],
          				"stellaris.harmonycloud.cn/multiclusterresourcebindings": ["get", "list"],
          				"stellaris.harmonycloud.cn/multiclusterresources": ["get", "list"],
          				"stellaris.harmonycloud.cn/multiclusterresourceschedulepolicies": ["get", "list"],
          				"stellaris.harmonycloud.cn/namespacemappings": ["get", "list"],
          				"stellaris.harmonycloud.cn/resourceaggregatepolicies": ["get", "list"],
          				"storage.k8s.io/csidrivers": ["get", "list"],
          				"storage.k8s.io/csinodes": ["get", "list"],
          				"storage.k8s.io/storageclasses": ["get", "list"],
          				"storage.k8s.io/volumeattachments": ["get", "list"],
          				"webhook.harmonycloud.cn/isolationwebhookconfigurations": ["get", "list"]
          			}
          		}
          	}]
          },
          {
          	"name": "产品管理",
          	"code": "unified_platform_sys_cloud_service",
          	"kind": "platform",
          	"icon": "v35_ProductManage",
          	"url": "/cloundserving/list",
          	"type": "menu",
          	"method": "get",
            "sort": 3,
          	"parent": {
          		"code": "platform",
          		"cloudServiceName": "unified-platform"
          	},
          	"annotation": {
          		"ceiling": true
          	},
          	"children": [{
          		"name": "查询",
          		"code": "query",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get",
          		"annotation": {
          			"ceiling": true
          		}
          	}]
          }]
    # 菜单 - 容器服务
    # 移除rbac授权和组织空间设置前的版本
    - name: container-service
      kind: permissionImport
      permissionConfig:
        app:
          code: application
        permissions: |-
          [{
          	"name": "集群监控",
          	"code": "unified_platform_sys_monitoring_centre",
          	"kind": "platform",
          	"icon": "v35_ClusterMonitor",
          	"url": "/monitoring",
          	"type": "menu",
          	"method": "get",
          	"sort": 4,
          	"parent": {
          		"code": "platform",
          		"cloudServiceName": "unified-platform"
          	},
          	"annotation": {},
          	"children": [{
          		"name": "查询",
          		"code": "query",
          		"kind": "platform",
          		"type": "permission",
          		"method": "post",
          		"annotation": {}
          	}]
          },
          {
          	"name": "门户告警",
          	"code": "unified_platform_sys_alarm_centre",
          	"kind": "platform",
          	"icon": "v35_PortalAlerts",
          	"url": "",
          	"type": "menu",
          	"method": "get",
          	"sort": 5,
          	"parent": {
          		"code": "platform",
          		"cloudServiceName": "unified-platform"
          	},
          	"children": [{
          		"name": "告警列表",
          		"code": "unified_platform_alert_rule_alarm_record",
          		"kind": "platform",
          		"url": "/monitor/alarm/list",
          		"type": "menu",
          		"method": "get",
          		"sort": 1,
          		"children": [{
          			"name": "查询",
          			"code": "query",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "处理",
          			"code": "exec",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		}]
          	},
          	{
          		"name": "告警规则",
          		"code": "unified_platform_alert_rule_policy",
          		"kind": "platform",
          		"url": "/monitor/alarm/rule",
          		"type": "menu",
          		"method": "get",
          		"sort": 2,
          		"children": [{
          			"name": "启用",
          			"code": "start",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
                {
          			"name": "停用",
          			"code": "stop",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
                {
          			"name": "新增",
          			"code": "add",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "删除",
          			"code": "remove",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "查询",
          			"code": "query",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "修改",
          			"code": "update",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "告警历史查看",
          			"code": "history_view",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "告警历史处理",
          			"code": "history_handle",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		}]
          	},
          	{
          		"name": "通知对象组",
          		"code": "unified_platform_alarm_template",
          		"kind": "platform",
          		"url": "/monitor/alarm/notifyGroup",
          		"type": "menu",
          		"method": "get",
          		"sort": 3,
          		"children": [{
          			"name": "新增",
          			"code": "add",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "删除",
          			"code": "remove",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "查询",
          			"code": "query",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "修改",
          			"code": "update",
          			"kind": "platform",
          			"type": "permission",
          			"method": "post"
          		}]
          	}]
          },
          {
          	"name": "门户日志",
          	"code": "unified_platform_sys_log_centre",
          	"kind": "platform",
          	"icon": "v35_PortalLog",
          	"url": "",
          	"type": "menu",
          	"method": "get",
          	"sort": 6,
          	"parent": {
          		"code": "platform",
          		"cloudServiceName": "unified-platform"
          	},
          	"children": [{
          		"name": "日志查询",
          		"code": "unified_platform_sys_log_query",
          		"kind": "platform",
          		"url": "/log/logQuery",
          		"type": "menu",
          		"method": "get",
          		"sort": 1,
          		"annotation": {
          			"resources": ["stellaris.harmonycloud.cn/clusters", "namespaces", "resourcequotas", "events", "apps/deployments", "apps/statefulsets", "apps/daemonsets", "apps/replicasets", "batch/jobs", "batch/cronjobs", "pods", "nodes"]
          		},
          		"children": [{
          			"name": "查询",
          			"code": "query",
          			"kind": "platform",
          			"url": "/apps/{appName}/applogs/filenames",
          			"type": "permission",
          			"method": "get",
          			"annotation": {
          				"resource_option": {
          					"apps/daemonsets": ["get", "list"],
          					"apps/deployments": ["get", "list"],
          					"apps/replicasets": ["get", "list"],
          					"apps/statefulsets": ["get", "list"],
          					"batch/cronjobs": ["get", "list"],
          					"batch/jobs": ["get", "list"],
          					"events": ["get", "list"],
          					"namespaces": ["get", "list"],
          					"nodes": ["get", "list"],
          					"pods": ["get", "list"],
          					"resourcequotas": ["get", "list"],
          					"stellaris.harmonycloud.cn/clusters": ["get", "list"]
          				}
          			}
          		},
          		{
          			"name": "导出",
          			"code": "export",
          			"kind": "platform",
          			"url": "/apps/{appName}/applogs/export",
          			"type": "permission",
          			"method": "get"
          		}]
          	},
          	{
          		"name": "日志备份",
          		"code": "unified_platform_log_backup",
          		"kind": "platform",
          		"url": "/log/logBackup",
          		"type": "menu",
          		"method": "get",
          		"sort": 2,
          		"children": [{
          			"name": "查询",
          			"code": "query",
          			"kind": "platform",
          			"url": "/snapshotrules",
          			"type": "permission",
          			"method": "get"
          		},
          		{
          			"name": "创建规则",
          			"code": "add-backuprule",
          			"kind": "platform",
          			"url": "/snapshotrules",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "创建快照",
          			"code": "add-snapshot",
          			"kind": "platform",
          			"url": "/snapshotrules/snapshots",
          			"type": "permission",
          			"method": "post"
          		},
          		{
          			"name": "操作规则",
          			"code": "edit-backuprule",
          			"kind": "platform",
          			"url": "/snapshotrules",
          			"type": "permission",
          			"method": "put"
          		},
          		{
          			"name": "恢复备份",
          			"code": "restore-snapshot",
          			"kind": "platform",
          			"url": "/snapshotrules/snapshots",
          			"type": "permission",
          			"method": "put"
          		},
          		{
          			"name": "删除快照",
          			"code": "delete-snapshot",
          			"kind": "platform",
          			"url": "/snapshotrules/snapshots",
          			"type": "permission",
          			"method": "get"
          		},
          		{
          			"name": "删除备份记录",
          			"code": "delete-restore",
          			"kind": "platform",
          			"url": "/snapshotrules/snapshots/restored/{date}",
          			"type": "permission",
          			"method": "get"
          		}]
          	},
          	{
          		"name": "系统日志",
          		"code": "unified_platform_system_log",
          		"kind": "platform",
          		"url": "/log/systemLog",
          		"type": "menu",
          		"method": "get",
          		"sort": 3,
          		"annotation": {
          			"resource_option": {
          				"apps/daemonsets": ["get", "list"],
          				"apps/deployments": ["get", "list"],
          				"apps/replicasets": ["get", "list"],
          				"apps/statefulsets": ["get", "list"],
          				"batch/cronjobs": ["get", "list"],
          				"batch/jobs": ["get", "list"],
          				"events": ["get", "list"],
          				"namespaces": ["get", "list"],
          				"nodes": ["get", "list"],
          				"pods": ["get", "list"],
          				"resourcequotas": ["get", "list"],
          				"stellaris.harmonycloud.cn/clusters": ["get", "list"]
          			}
          		},
          		"children": [{
          			"name": "查询",
          			"code": "query",
          			"kind": "platform",
          			"url": "/deploys/{deployName}/logs/filenames",
          			"type": "permission",
          			"method": "get"
          		},
          		{
          			"name": "导出",
          			"code": "export",
          			"kind": "platform",
          			"url": "/deploys/{deployName}/logs/export",
          			"type": "permission",
          			"method": "get"
          		}]
          	}]
          },

            {
              "name": "门户灾备",
              "code": "unified_platform_sys_disaster_recovery",
              "kind": "platform",
              "url": "/systemaudit/disasterRecovery",
              "type": "menu",
              "method": "get",
              "sort": 1,
              "parent": {
                  "code": "unified_operator_maintenance",
                  "cloudServiceName": "unified-platform"
              },
              "annotation": {
                "resources": ["harmonycloud.cn/disasterrecoveries", "stellaris.harmonycloud.cn/clusters"]
              },
              "children": [{
                "name": "查询",
                "code": "query",
                "kind": "platform",
                "url": "/system/disaster/recovery,/system/disaster/recovery/log",
                "type": "permission",
                "method": "get",
                "annotation": {
                  "resource_option": {
                    "harmonycloud.cn/disasterrecoveries": ["get"],
                    "stellaris.harmonycloud.cn/clusters": ["get", "list", "watch"]
                  }
                }
              },
              {
                "name": "配置",
                "code": "configuration",
                "kind": "platform",
                "url": "/system/disaster/recovery",
                "type": "permission",
                "method": "put",
                "annotation": {
                  "resource_option": {
                    "harmonycloud.cn/disasterrecoveries": ["get", "update"],
                    "stellaris.harmonycloud.cn/clusters": ["get", "list", "watch"]
                  }
                }
              },
              {
                "name": "切换",
                "code": "recovery",
                "kind": "platform",
                "url": "/system/disaster/recovery",
                "type": "permission",
                "method": "post",
                "annotation": {
                  "resource_option": {
                    "harmonycloud.cn/disasterrecoveries": ["get", "update"],
                    "stellaris.harmonycloud.cn/clusters": ["get", "list", "watch"]
                  }
                }
              }]
            },
           {
              "name": "备份还原",
              "code": "unified_platform_sys_backup_and_recovery",
              "kind": "platform",
              "url": "/systemaudit/backupRecovery",
              "type": "menu",
              "method": "get",
              "sort": 4,
              "parent": {
                  "code": "unified_operator_maintenance",
                  "cloudServiceName": "unified-platform"
              },
              "annotation": {
                "resources": ["velero.io/backups", "velero.io/restores", "velero.io/schedules", "velero.io/backupstoragelocations", "velero.io/downloadrequests", "velero.io/podvolumebackups", "velero.io/podvolumerestores", "velero.io/deletebackuprequests", "stellaris.harmonycloud.cn/clusters", "namespaces", "secrets"]
              },
              "children": [{
                "name": "查询",
                "code": "query",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/schedules",
                "type": "permission",
                "method": "get",
                "annotation": {
                  "resource_option": {
                    "secrets": ["get", "list"],
                    "velero.io/backups": ["get", "list"],
                    "velero.io/backupstoragelocations": ["get", "list"],
                    "velero.io/downloadrequests": ["get", "list"],
                    "velero.io/podvolumebackups": ["get", "list"],
                    "velero.io/schedules": ["get", "list"]
                  }
                }
              },
              {
                "name": "备份策略新增",
                "code": "unified_platform_schedules_add",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/schedules",
                "type": "permission",
                "method": "post",
                "annotation": {
                  "resource_option": {
                    "secrets": ["get", "list", "create", "delete"],
                    "velero.io/backupstoragelocations": ["get", "list", "create", "delete"],
                    "velero.io/schedules": ["get", "list", "create"]
                  }
                }
              },
              {
                "name": "备份策略编辑",
                "code": "unified_platform_schedules_edit",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}",
                "type": "permission",
                "method": "put",
                "annotation": {
                  "resource_option": {
                    "velero.io/schedules": ["get", "list", "update", "patch"]
                  }
                }
              },
              {
                "name": "备份策略立即执行",
                "code": "unified_platform_schedules_execute",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}/execute",
                "type": "permission",
                "method": "post",
                "annotation": {
                  "resource_option": {
                    "velero.io/schedules": ["get", "list", "update", "patch"]
                  }
                }
              },
              {
                "name": "备份策略启动",
                "code": "unified_platform_schedules_start",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}/start",
                "type": "permission",
                "method": "post",
                "annotation": {
                  "resource_option": {
                    "velero.io/schedules": ["get", "list", "update", "patch"]
                  }
                }
              },
              {
                "name": "备份策略停止",
                "code": "unified_platform_schedules_stop",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}/stop",
                "type": "permission",
                "method": "post",
                "annotation": {
                  "resource_option": {
                    "velero.io/schedules": ["get", "list", "update", "patch"]
                  }
                }
              },
              {
                "name": "备份策略同步备份服务器信息",
                "code": "unified_platform_schedules_sync_storage_server",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}/syncStorageServer",
                "type": "permission",
                "method": "post",
                "annotation": {
                  "resource_option": {
                    "secrets": ["get", "list", "update", "patch", "delete"],
                    "velero.io/backupstoragelocations": ["get", "list", "update", "patch", "delete"]
                  }
                }
              },
              {
                "name": "备份策略删除记录",
                "code": "unified_platform_schedules_backups_remove",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}/backups/{backupName}",
                "type": "permission",
                "method": "get",
                "annotation": {
                  "resource_option": {
                    "velero.io/backups": ["get", "list"],
                    "velero.io/deletebackuprequests": ["get", "list", "create"]
                  }
                }
              },
              {
                "name": "备份策略删除",
                "code": "unified_platform_schedules_remove",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}",
                "type": "permission",
                "method": "get",
                "annotation": {
                  "resource_option": {
                    "velero.io/schedules": ["get", "list", "delete"]
                  }
                }
              },
              {
                "name": "恢复策略新增",
                "code": "unified_platform_restores_template_add",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/restoresTemplate",
                "type": "permission",
                "method": "post",
                "annotation": {
                  "resource_option": {
                    "velero.io/restores": ["get", "list", "create"]
                  }
                }
              },
              {
                "name": "恢复策略重试",
                "code": "unified_platform_restores_template_retry",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/restoresTemplate/{restoresTemplateId}/retry",
                "type": "permission",
                "method": "post",
                "annotation": {
                  "resource_option": {
                    "velero.io/restores": ["get", "list", "create"]
                  }
                }
              },
              {
                "name": "恢复策略删除",
                "code": "unified_platform_restores_template_remove",
                "kind": "platform",
                "url": "/apis/v1/system/backuprestore/restoresTemplate/{restoresTemplateId}",
                "type": "permission",
                "method": "get",
                "annotation": {
                  "resource_option": {
                    "velero.io/restores": ["get", "list", "delete"]
                  }
                }
              }]
            },
            {
              "name": "备份服务器",
              "code": "unified_platform_sys_backup_setting",
              "kind": "platform",
              "icon": "maintance-setting",
              "url": "/system/backupServer",
              "type": "menu",
              "method": "get",
              "sort": 6,
              "parent": {
                  "code": "unified_platform_sys_platform_mgr",
                  "cloudServiceName": "unified-platform"
              },
              "children": [{
                "name": "查询备份服务器",
                "code": "query",
                "kind": "platform",
                "url": "/apis/v1/system/storageServers",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "新增备份服务器",
                "code": "unified_platform_storage_server_add",
                "kind": "platform",
                "url": "/apis/v1/system/storageServers",
                "type": "permission",
                "method": "post"
              },
              {
                "name": "编辑备份服务器",
                "code": "unified_platform_storage_server_edit",
                "kind": "platform",
                "url": "/apis/v1/system/storageServers/{storageServersId}",
                "type": "permission",
                "method": "put"
              },
              {
                "name": "删除备份服务器",
                "code": "unified_platform_storage_server_remove",
                "kind": "platform",
                "url": "/apis/v1/system/storageServers/{storageServersId}",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "查询存储桶",
                "code": "unified_platform_storage_bucket_query",
                "kind": "platform",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "新增存储桶",
                "code": "unified_platform_storage_bucket_add",
                "kind": "platform",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "编辑存储桶",
                "code": "unified_platform_storage_bucket_edit",
                "kind": "platform",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "删除存储桶",
                "code": "unified_platform_storage_bucket_remove",
                "kind": "platform",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "检查存储桶",
                "code": "unified_platform_storage_bucket_check",
                "kind": "platform",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "配额管理",
                "code": "unified_platform_storage_bucket_assign",
                "kind": "platform",
                "type": "permission",
                "method": "get"
              }]
            },
            {
              "name": "集群巡检",
              "code": "unified_platform_sys_cluster_patrol",
              "kind": "platform",
              "url": "/systemaudit/clusterPatrol",
              "type": "menu",
              "method": "get",
              "sort": 6,
              "parent": {
                  "code": "unified_operator_maintenance",
                  "cloudServiceName": "unified-platform"
              },
              "annotation": {
                "resources": ["stellaris.harmonycloud.cn/clusters"]
              },
              "children": [{
                "name": "查询",
                "code": "query",
                "kind": "platform",
                "url": "/metricsreport/cluster/getreport",
                "type": "permission",
                "method": "get",
                "annotation": {
                  "resource_option": {
                    "stellaris.harmonycloud.cn/clusters": ["get", "list"]
                  }
                }
              },
              {
                "name": "巡检",
                "code": "patrol",
                "kind": "platform",
                "url": "/metricsreport/cluster/getreport",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "详情",
                "code": "detail",
                "kind": "platform",
                "url": "/dashboards",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "导出",
                "code": "export",
                "kind": "platform",
                "url": "/metricsreport/cluster/exportreport",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "设置接收人",
                "code": "setreceiver",
                "kind": "platform",
                "url": "/cluster/{clusterName}/health/monitor",
                "type": "permission",
                "method": "put"
              },
              {
                "name": "新增指标",
                "code": "addrule",
                "kind": "platform",
                "url": "/cluster/{clusterName}/health/thresholds/{metricsName}",
                "type": "permission",
                "method": "post"
              },
              {
                "name": "删除指标",
                "code": "delete",
                "kind": "platform",
                "url": "/cluster/{clusterName}/health/thresholds",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "编辑指标",
                "code": "edit",
                "kind": "platform",
                "url": "/cluster/{clusterName}/health/thresholds/{metricsName}",
                "type": "permission",
                "method": "put"
              }]
            },
            {
              "name": "故障隔离",
              "code": "unified_platform_faultsolation",
              "kind": "platform",
              "url": "/systemaudit/faultsolation",
              "type": "menu",
              "method": "get",
              "sort": 7,
              "parent": {
                  "code": "unified_operator_maintenance",
                  "cloudServiceName": "unified-platform"
              },
              "annotation": {
                "resources": ["stellaris.harmonycloud.cn/clusters", "namespaces", "resourcequotas", "events", "apps/deployments", "apps/statefulsets", "apps/daemonsets", "apps/replicasets", "batch/jobs", "batch/cronjobs", "pods", "nodes"]
              },
              "children": [{
                "name": "查询",
                "code": "query",
                "kind": "platform",
                "type": "permission",
                "method": "get",
                "annotation": {
                  "resource_option": {
                    "apps/daemonsets": ["get", "list"],
                    "apps/deployments": ["get", "list"],
                    "apps/replicasets": ["get", "list"],
                    "apps/statefulsets": ["get", "list"],
                    "batch/cronjobs": ["get", "list"],
                    "batch/jobs": ["get", "list"],
                    "events": ["get", "list"],
                    "namespaces": ["get", "list"],
                    "nodes": ["get", "list"],
                    "pods": ["get", "list"],
                    "resourcequotas": ["get", "list"],
                    "stellaris.harmonycloud.cn/clusters": ["get", "list"]
                  }
                }
              },
              {
                "name": "pod控制台",
                "code": "containerTerminal",
                "kind": "platform",
                "type": "permission",
                "method": "get"
              },
              {
                "name": "删除",
                "code": "remove",
                "kind": "platform",
                "type": "permission",
                "method": "get"
              }]
            },
            {
                "name": "备份仓库",
                "code": "sys_backup_repository",
                "kind": "platform",
                "url": "/system/backupRepository",
                "type": "menu",
                "method": "get",
                "sort": 8,
                "parent": {
                  "code": "unified_operator_maintenance",
                  "cloudServiceName": "unified-platform"
                },
                "children": [
                    {
                      "name": "新增",
                      "code": "add_sys_backup_repository",
                      "kind": "platform",
                      "type": "permission",
                      "method": "get"
                    },{
                      "name": "编辑",
                      "code": "edit_sys_backup_repository",
                      "kind": "platform",
                      "type": "permission",
                      "method": "get"
                    },{
                      "name": "删除",
                      "code": "remove_sys_backup_repository",
                      "kind": "platform",
                      "type": "permission",
                      "method": "get"
                    }
                ]
            }
          ]

    - name: organ_quota_mgr
      kind: permissionImport
      permissionConfig:
        app:
          code: application
        permissions: |-
          [{
                "name": "配额",
                "code": "orgQuotaManage",
                "kind": "platform",
                "type": "permission",
                "method": "get",
                "parent": {
                  "code":"orgList",
                  "cloudServiceName":"unified-platform"
                }
          }]
    - name: organ_manager
      kind: permissionImport
      permissionConfig:
        app:
          code: application
        permissions: |-
          [{"name":"租户总览","code":"unified_platform_sys_organ_overview","kind":"organ","icon":"organizational-space-overview-menu","url":"/organization/space/overview","type":"menu","method":"get","parent":{"code":"organ_inner","cloudServiceName":"unified-platform"},"sort":1,"annotation":{"resources":["apps/deployments","apps/statefulsets","apps/daemonsets","apps/replicasets","batch/jobs","batch/cronjobs","pods","services","secrets","configmaps","persistentvolumeclaims","persistentvolumes","storage.k8s.io/storageclasses","serviceaccounts","namespaces","nodes","resourcequotas","harmonycloud.cn/nodepools","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/networkresources"]},"children":[{"name":"查询","code":"query","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}}]},{"name":"项目列表","code":"unified_platform_sys_organ_project_list","kind":"organ","icon":"v35_ProjectManage","url":"/organization/space/projectList","type":"menu","method":"get","parent":{"code":"organ_inner","cloudServiceName":"unified-platform"},"sort":2,"children":[{"name":"查询","code":"query","kind":"organ","url":"/projects","type":"permission","method":"get"},{"name":"新增","code":"add","kind":"organ","url":"/organization/{organizationId}/projects","type":"permission","method":"post"},{"name":"配额管理","code":"quota_manage","kind":"organ","url":"/organization/space/projectList","type":"permission","method":"put"},{"name":"编辑","code":"edit","kind":"organ","url":"/organization/{organizationId}/projects/{id}","type":"permission","method":"put"},{"name":"删除","code":"remove","kind":"organ","url":"/organization/{organizationId}/projects/{id}","type":"permission","method":"delete"}]},{"name":"命名空间","code":"unified_platform_sys_organ_namespaces","kind":"organ","icon":"v35_Namespace","url":"/organization/space/namespaceList","type":"menu","method":"get","parent":{"code":"organ_inner","cloudServiceName":"unified-platform"},"sort":3,"annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","create","update","patch","delete"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","delete","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]},"resources":["namespaces","stellaris.harmonycloud.cn/clusters","harmonycloud.cn/nodepools","apps/deployments","resourcequotas","isolate.harmonycloud.cn/isolatelocks","apps/statefulsets","events","pods","nodes","batch/jobs","batch/cronjobs","apps/daemonsets","isolate.harmonycloud.cn/hleases"]},"children":[{"name":"查询","code":"query","kind":"organ","url":"/organizations/{organizationId}/namespaces,/organizations/{organizationId}/namespaces/filters","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"租户空间配额","code":"unified_platform_sys_organ_quota","kind":"organ","icon":"v35_ResourceQuota","url":"","type":"menu","method":"get","parent":{"code":"organ_inner","cloudServiceName":"unified-platform"},"sort":4,"children":[{"name":"资源池","code":"unified_platform_sys_organ_node_pool","kind":"organ","url":"/organization/space/quotacenter/poolList","type":"menu","method":"get","annotation":{"resources":["nodes","stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","isolate.harmonycloud.cn/isolatelocks","isolate.harmonycloud.cn/hleases","harmonycloud.cn/nodepools"]},"children":[{"name":"查询","code":"query","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"分配项目","code":"assign_to_project","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"添加项目配额","code":"add_quota","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑项目配额","code":"edit_quota","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除项目配额","code":"remove_quota","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"remove","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch","delete"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"存储服务","code":"unified_platform_sys_organ_storage","kind":"organ","url":"/organization/space/quotacenter/storageList","type":"menu","method":"get","annotation":{"resources":["storage.k8s.io/storageclasses","persistentvolumeclaims","secrets","pods","apps/deployments","persistentvolumes","resourcequotas","stellaris.harmonycloud.cn/clusters","services","events","namespaces","configmaps"]},"children":[{"name":"查询","code":"query","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"分配项目","code":"assign_to_project","kind":"organ","type":"permission","method":"post","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"name":"添加项目配额","code":"add_quota","kind":"organ","type":"permission","method":"post","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"name":"编辑项目配额","code":"edit_quota","kind":"organ","type":"permission","method":"put","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"name":"删除项目配额","code":"remove_quota","kind":"organ","type":"permission","method":"delete","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"name":"删除","code":"remove","kind":"organ","type":"permission","method":"delete","annotation":{"resource_option":{"apps/deployments":["get","list","delete"],"configmaps":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","delete"],"persistentvolumes":["get","list","delete"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list","delete"],"services":["get","list","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","delete"]}}}]},{"name":"网络域","code":"unified_platform_sys_organ_network_area","kind":"organ","url":"/organization/space/quotacenter/networkarea","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings","events","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/hdsvcs","isolate.harmonycloud.cn/hleases","mystra.heimdallr.harmonycloud.cn/podpolicies","configmaps","pods"]},"children":[{"name":"查询","code":"query","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"分配项目","code":"assign_to_project","kind":"organ","type":"permission","method":"post","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"remove_quota","kind":"organ","type":"permission","method":"delete","annotation":{"resource_option":{"configmaps":["get","list","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete"],"isolate.harmonycloud.cn/hleases":["get","list","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"name":"网络IP池","code":"unified_platform_sys_organ_network_ip_pool","kind":"organ","url":"/organization/space/quotacenter/ippool","type":"menu","method":"get","annotation":{"resources":["isolate.harmonycloud.cn/hleases","resourcequotas","namespaces","events","stellaris.harmonycloud.cn/clusters","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/hdareas","stellaris.harmonycloud.cn/multiclusterresources","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/networkresources","pods","mystra.heimdallr.harmonycloud.cn/podpolicies","configmaps","heimdallr.harmonycloud.cn/hdsvcs","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/hdpods","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"name":"查询","code":"query","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"分配项目","code":"assign_to_project","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"remove_quota","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete"],"isolate.harmonycloud.cn/hleases":["get","list","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"name":"虚拟机网络","code":"unified_platform_sys_organ_vm_network","kind":"organ","url":"/organization/space/quotacenter/virtualmachine","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","kubeovn.io/vlans","kubeovn.io/subnets","kubeovn.io/ips","kubevirt.io/virtualmachines"]},"children":[{"name":"查询","code":"query","kind":"organ","type":"permission","method":"post","annotation":{"resource_option":{"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list"],"kubeovn.io/vlans":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"分配项目","code":"assign_to_project","kind":"organ","type":"permission","method":"post","annotation":{"resource_option":{"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list","update"],"kubeovn.io/vlans":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"负载均衡","code":"unified_platform_sys_organ_lb","kind":"organ","url":"/organization/space/quotacenter/loadbalance","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","services","expose.helper.harmonycloud.cn/layer4exposes","networking.k8s.io/ingresses","expose.helper.harmonycloud.cn/ingressclasses","apisix.apache.org/apisixroutes","configmaps","secrets","endpoints","pods","nodes"]},"children":[{"name":"查询","code":"query","kind":"organ","type":"permission","method":"get","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list"],"configmaps":["get","list"],"endpoints":["get","list"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"分配项目","code":"assign_to_project","kind":"organ","type":"permission","method":"put","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"remove","kind":"organ","type":"permission","method":"delete","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch","delete"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch","delete"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch","delete"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch","delete"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch","delete"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"制品服务","code":"unified_platform_sys_organ_registry","kind":"organ","url":"/organization/space/quotacenter/registry","type":"menu","method":"get","children":[{"name":"查询","code":"query","kind":"organ","type":"permission","method":"get"},{"name":"分配项目","code":"assign_to_project","kind":"organ","type":"permission","method":"get"},{"name":"删除","code":"remove","kind":"organ","type":"permission","method":"get"}]},{"name":"备份服务器","code":"unified_platform_sys_organ_backup","kind":"organ","url":"/organization/space/backupServer","type":"menu","method":"get","children":[{"name":"查询","code":"unified_platform_organ_storage_server_list_query","kind":"organ","type":"permission","method":"get"},{"name":"配额管理","code":"unified_organ_storage_bucket_assign","kind":"organ","type":"permission","method":"get"}]},{"name":"证书管理","code":"unified_platform_sys_organ_certificate_manager","kind":"organ","url":"/organization/space/quotacenter/certificate","type":"menu","method":"get","children":[{"name":"查询","code":"skyview_organ_certificate_query","kind":"organ","type":"permission","method":"get"},{"name":"分配项目","code":"skyview_organ_certificate_assign","kind":"organ","type":"permission","method":"get"}]}]}]
    # 菜单 - 消息中心
    - name: message
      kind: permissionImport
      permissionConfig:
        app:
          code: message_manager
        permissions: |-
          [{
          	"name": "站内信管理",
          	"code": "unified_platform_sys_website_in_mail_manage",
          	"kind": "platform",
          	"url": "/messageCenter/messageManage",
          	"type": "menu",
          	"method": "get",
            "icon": "v35_WebsiteManage",
          	"children": [{
          		"name": "新增",
          		"code": "add",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	},
          	{
          		"name": "编辑",
          		"code": "edit",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	},
          	{
          		"name": "撤回/发送",
          		"code": "sent",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	},
          	{
          		"name": "删除",
          		"code": "delete",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	}]
          },
          {
          	"name": "机器人订阅",
          	"code": "unified_platform_sys_robot_notice_manage",
          	"kind": "platform",
          	"url": "/messageCenter/robotList",
          	"type": "menu",
          	"method": "get",
            "icon": "v35_RobotNotice",
          	"children": [{
          		"name": "配置",
          		"code": "config",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	}]
          },
          {
          	"name": "接收人管理",
          	"code": "unified_platform_sys_receiver_manager",
          	"kind": "platform",
          	"url": "/messageCenter/recipientManage",
          	"type": "menu",
          	"method": "get",
            "icon": "v35_ReceiverManage",
          	"children": [{
          		"name": "配置",
          		"code": "config",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	}]
          },
          {
          	"name": "接收渠道配置",
          	"code": "unified_platform_sys_message_channel_config",
          	"kind": "platform",
          	"url": "/messageCenter/channelConfig",
          	"type": "menu",
          	"method": "get",
            "icon": "v35_MessageChannel",
          	"children": [{
          		"name": "邮箱编辑",
          		"code": "emailEdit",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	},
          	{
          		"name": "邮箱启用/禁用",
          		"code": "emailEnable",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	},
          	{
          		"name": "钉钉启用/禁用",
          		"code": "dingtalkEnable",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	},
          	{
          		"name": "机器人创建",
          		"code": "robotAdd",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	},
          	{
          		"name": "机器人启用/禁用",
          		"code": "robotEnable",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	},
          	{
          		"name": "机器人编辑",
          		"code": "robotEdit",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	},
          	{
          		"name": "机器人删除",
          		"code": "robotDelete",
          		"kind": "platform",
          		"type": "permission",
          		"method": "get"
          	}]
          }]