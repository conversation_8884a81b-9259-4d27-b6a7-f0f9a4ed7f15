apiVersion: cloud-service.harmonycloud.cn/v1alpha1
kind: Material
metadata:
  name: olympus-unified-platform-3
  annotations:
    message.material.harmonycloud.cn/reapply: "true"
spec:
  cloudServiceName: unified-platform
  waitPolicy:
    onSuccess:
      - name: olympus-unified-platform-2
  tasks:
    # 菜单 - 项目中心
    - name: project-center
      kind: permissionImport
      permissionConfig:
        app:
          code: project_mgr
        permissions: |-
          [{"name":"命名空间","code":"unified_platform_sys_project_namespaces","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_Namespace","url":"/project/space/namespaceList","type":"menu","method":"get","annotation":{"resources":["limitranges","nodes","stellaris.harmonycloud.cn/clusters","namespaces","events","isolate.harmonycloud.cn/isolatelocks","isolate.harmonycloud.cn/hleases","harmonycloud.cn/nodepools","pods","resourcequotas","apps/deployments","apps/statefulsets","apps/daemonsets","batch/cronjobs","batch/jobs","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/organizations/{organizationId}/projects/{projectId}/namespaces,/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace},/organizations/{organizationId}/projects/{projectId}/namespaces/filters","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","patch","create","update"],"limitranges":["get","list","patch","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create","update","patch","delete"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create","update","patch","delete"]}}},{"name":"新增","code":"add","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces","type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list","patch","create","update"],"limitranges":["get","list","patch","create","update"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","patch","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"配额管理","code":"quota_manage","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespaces/{namespace}","type":"permission","method":"put","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list","patch","create","update"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","patch","create","update"],"limitranges":["get","list","patch","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑基础信息","code":"editDescription","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespaces/{namespaces}/description","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list","patch","create","update"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","patch","create","update"],"limitranges":["get","list","patch","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑元数据","code":"edit","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespaces/{namespaces}","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list","patch","create","update"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","patch","create","update"],"limitranges":["get","list","patch","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"remove","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespaces/{namespaces}","type":"permission","method":"delete","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/statefulsets":["list","get"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list","patch","create","update","delete"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list","patch","create","update","delete"],"limitranges":["get","list","patch","create","update"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"多集群命名空间查询","code":"skyview_multi_cluster_namespace_list","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"多集群命名空间新增","code":"skyview_multi_cluster_namespace_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"多集群命名空间配额管理","code":"skyview_multi_cluster_namespace_quota_manage","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"put"},{"name":"多集群命名空间删除","code":"skyview_multi_cluster_namespace_delete","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete"}]},{"name":"项目空间配额","code":"unified_platform_sys_project_quota","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_ProjectConfig","url":"","type":"menu","method":"get","children":[{"name":"资源池","code":"unified_platform_sys_project_node_pool","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/quotacenter/poolList","type":"menu","method":"get","annotation":{"resources":["nodes","stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","isolate.harmonycloud.cn/isolatelocks","isolate.harmonycloud.cn/hleases","harmonycloud.cn/nodepools"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"分配命名空间","code":"assign_to_namespace","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑命名空间配额","code":"edit_quota","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除命名空间配额","code":"remove_quota","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list","update","patch","delete"],"isolate.harmonycloud.cn/hleases":["get","list"],"isolate.harmonycloud.cn/isolatelocks":["get","list"],"namespaces":["get","list"],"nodes":["get","list","update","patch"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"存储服务","code":"unified_platform_sys_project_storage","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/quotacenter/storageList","type":"menu","method":"get","annotation":{"resources":["persistentvolumes","configmaps","storage.k8s.io/storageclasses","secrets","namespaces","pods","apps/deployments","events","resourcequotas","services","persistentvolumeclaims","stellaris.harmonycloud.cn/clusters"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"分配命名空间","code":"assign_to_namespace","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"name":"添加命名空间配额","code":"add_quota","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"name":"编辑命名空间配额","code":"edit_quota","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"name":"删除命名空间配额","code":"remove_quota","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","update","patch"]}}},{"name":"删除","code":"remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","delete"],"configmaps":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","delete"],"persistentvolumes":["get","list","delete"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list","delete"],"services":["get","list","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list","delete"]}}}]},{"name":"网络域","code":"unified_platform_sys_project_network_area","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/quotacenter/networkarea","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings","events","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/hdsvcs","isolate.harmonycloud.cn/hleases","mystra.heimdallr.harmonycloud.cn/podpolicies","configmaps","pods"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"分配命名空间","code":"assign_to_namespace","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"remove_quota","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete"],"isolate.harmonycloud.cn/hleases":["get","list","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"name":"网络IP池","code":"unified_platform_sys_project_network_ip_pool","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/quotacenter/ippool","type":"menu","method":"get","annotation":{"resources":["configmaps","stellaris.harmonycloud.cn/multiclusterresourcebindings","mystra.heimdallr.harmonycloud.cn/podpolicies","heimdallr.harmonycloud.cn/networkresources","pods","heimdallr.harmonycloud.cn/hdpods","stellaris.harmonycloud.cn/clusters","heimdallr.harmonycloud.cn/networkdetails","namespaces","isolate.harmonycloud.cn/hleases","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdsvcs","events","resourcequotas","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdpools","stellaris.harmonycloud.cn/multiclusterresources"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"分配命名空间","code":"assign_to_namespace","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","update","patch","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"remove_quota","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete"],"isolate.harmonycloud.cn/hleases":["get","list","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"]}}}]},{"name":"虚拟机网络","code":"unified_platform_sys_workspace_vm_network","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/quotacenter/virtualmachine","type":"menu","method":"get","children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"}]},{"name":"制品服务","code":"unified_platform_sys_project_registry","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/quotacenter/registry","type":"menu","method":"get","children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"删除","code":"remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"}]},{"name":"负载均衡","code":"unified_platform_sys_project_lb","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/quotacenter/loadbalance","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","services","expose.helper.harmonycloud.cn/layer4exposes","networking.k8s.io/ingresses","expose.helper.harmonycloud.cn/ingressclasses","apisix.apache.org/apisixroutes","configmaps","secrets","endpoints","pods","nodes"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list"],"configmaps":["get","list"],"endpoints":["get","list"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list","create","update","patch","delete"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","create","update","patch","delete"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create","update","patch","delete"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","create","update","patch","delete"],"nodes":["get","list","update","patch"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch","delete"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"备份服务器","code":"unified_platform_sys_project_backup","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/backupServer","type":"menu","method":"get","annotation":{},"children":[{"name":"查询","code":"unified_platform_project_storage_server_list_query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"}]},{"name":"证书管理","code":"unified_platform_sys_project_certificate_manager","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/quotacenter/certificate","type":"menu","method":"get","annotation":{},"children":[{"name":"查询","code":"skyview_project_certificate_query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"}]}]},{"name":"项目空间设置","code":"unified_platform_sys_project_settings","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_ProjectSetting","url":"/project/space/set/user","type":"menu","method":"get","children":[{"name":"项目用户","code":"unified_platform_sys_project_users","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/set/user","type":"menu","method":"get","annotation":{"resources":["rbac.authorization.k8s.io/rolebindings"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/users/page","type":"permission","method":"get"},{"name":"新增","code":"add","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/user-organ-project","type":"permission","method":"post","annotation":{"resource_option":{"rbac.authorization.k8s.io/rolebindings":["get","list","create"]}}},{"name":"关联角色","code":"bindRole","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/user-roles/users/{userId}/roles/{roleId}","type":"permission","method":"post","annotation":{"resource_option":{"rbac.authorization.k8s.io/rolebindings":["get","list","create","update","patch"]}}},{"name":"删除","code":"remove","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/user-organ-project","type":"permission","method":"delete","annotation":{"resource_option":{"rbac.authorization.k8s.io/rolebindings":["get","list","delete"]}}}]},{"name":"项目配置","code":"unified_platform_sys_project_remote","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/set/allowRemote","type":"menu","method":"get","children":[{"name":"编辑","code":"edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete"}]}]}]
    # 菜单 - 运维中心
    - name: operation-service
      kind: permissionImport
      permissionConfig:
        app:
          code: operation_center
        permissions: |-
          [{"name":"日志查询","code":"unified_platform_workspace_log_query","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_LogSearch","url":"/devops/space/logQuery","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","apps/deployments","apps/statefulsets","apps/daemonsets","apps/replicasets","batch/jobs","batch/cronjobs","pods","nodes"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/apps/{appName}/applogs/filenames","type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"告警中心","code":"unified_platform_workspace_project_alarm_center","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_AlertsCenter","url":"","type":"menu","method":"get","children":[{"name":"告警列表","code":"unified_platform_workspace_project_alarm_center_alarm_record","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/monitor/alarm/list","type":"menu","method":"get","children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"处理","code":"exec","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"}]},{"name":"告警规则","code":"unified_platform_workspace_project_alarm_center_alarm_policy","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/monitor/alarm/rule","type":"menu","method":"get","children":[{"name":"启用","code":"start","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"停用","code":"stop","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"新增","code":"add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"删除","code":"remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"修改","code":"update","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"}]},{"name":"通知对象组","code":"unified_platform_workspace_project_alarm_center_notice_temp","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/monitor/alarm/notifyGroup","type":"menu","method":"get","children":[{"name":"新增","code":"add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"删除","code":"remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"修改","code":"update","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"}]}]}]