apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    unified-platform.harmonycloud.cn/translate: ""
    unified-platform.harmonycloud.cn/cloudservice-name: unified-platform
    audit/component: olympus-oam
    version: v1.1.0
  name: audit-load-cm-container-service-olympus-oam-v1.1.0
data:
  translate.json: |-
    [{"operator":"add","configs":[{"regexTranslates":[{"groupName":"platform-alert","regex":"平台管理通知模板创建,通知模板:{0}","languageMap":{"en-US":"Platform management notification template creation, notification template: {0}","zh-HK":"平臺管理通知範本創建，通知範本：{0}"}},{"groupName":"platform-alert","regex":"平台管理通知模板编辑,通知模板:{0}","languageMap":{"en-US":"Platform management notification template editing, notification template: {0}","zh-HK":"平臺管理通知範本編輯，通知範本：{0}"}},{"groupName":"platform-alert","regex":"平台管理通知模板删除","languageMap":{"en-US":"Platform management notification template deletion","zh-HK":"平臺管理通知範本删除"}},{"groupName":"platform-alert","regex":"平台管理告警记录处理","languageMap":{"en-US":"Platform management alarm record processing","zh-HK":"平臺管理告警記錄處理"}},{"groupName":"workspace-alert","regex":"项目空间告警记录处理,项目id:{0}","languageMap":{"en-US":"Project Space Alarm Record Processing, Project ID: {0}","zh-HK":"項目空間告警記錄處理，項目id:{0}"}},{"groupName":"workspace-alert","regex":"项目空间通知模板编辑,项目id:{0},通知模板:{1}","languageMap":{"en-US":"Project Space Notification Template Editor, Project ID: {0}, Notification Template: {1}","zh-HK":"項目空間通知範本編輯，項目id:{0}，通知範本：{1}"}},{"groupName":"workspace-alert","regex":"项目空间通知模板删除,项目id:{0}","languageMap":{"en-US":"Project space notification template deleted, project id: {0}","zh-HK":"項目空間通知範本删除，項目id:{0}"}},{"groupName":"workspace-alert","regex":"项目空间通知模板创建,项目id:{0},通知模板:{1}","languageMap":{"en-US":"Project space notification template creation, project id: {0}, notification template: {1}","zh-HK":"項目空間通知範本創建，項目id:{0}，通知範本：{1}"}},{"groupName":"platform-alert-policy","regex":"平台管理告警策略批量启用","languageMap":{"en-US":"Batch activation of platform management alarm policies","zh-HK":"平臺管理告警策略批量啟用"}},{"groupName":"platform-alert-policy","regex":"平台管理告警策略编辑平台管理告警策略创建,策略名称:{0}","languageMap":{"en-US":"Platform Management Alarm Policy Editing Platform Management Alarm Policy Creation, Policy Name: {0}","zh-HK":"平臺管理告警策略編輯平臺管理告警策略創建，策略名稱：{0}"}},{"groupName":"platform-alert-policy","regex":"平台管理告警策略创建,策略名称:{0}","languageMap":{"en-US":"Platform management alarm policy creation, policy name: {0}","zh-HK":"平臺管理告警策略創建，策略名稱：{0}"}},{"groupName":"platform-alert-policy","regex":"平台管理告警策略批量停用","languageMap":{"en-US":"Batch deactivation of platform management alarm strategy","zh-HK":"平臺管理告警策略批量停用"}},{"groupName":"platform-alert-policy","regex":"平台管理告警批量策略删除","languageMap":{"en-US":"Platform management alarm batch strategy deletion","zh-HK":"平臺管理告警批量策略删除"}},{"groupName":"workspace-alert-policy","regex":"项目空间告警策略批量启用,项目id:{0}","languageMap":{"en-US":"Batch activation of project space alarm strategy, project ID: {0}","zh-HK":"項目空間告警策略批量啟用，項目id:{0}"}},{"groupName":"workspace-alert-policy","regex":"项目空间告警策略编辑项目空间告警策略创建,项目id:{0},策略名称:{1}","languageMap":{"en-US":"Project Space Alert Strategy Editing Project Space Alert Strategy Creation, Project ID: {0}, Policy Name: {1}","zh-HK":"項目空間告警策略編輯項目空間告警策略創建，項目id:{0}，策略名稱：{1}"}},{"groupName":"workspace-alert-policy","regex":"项目空间告警策略批量停用,项目id:{0}","languageMap":{"en-US":"Batch deactivation of project space alarm strategy, project ID: {0}","zh-HK":"項目空間告警策略批量停用，項目id:{0}"}},{"groupName":"workspace-alert-policy","regex":"项目空间告警策略创建,项目id:{0},策略名称:{1}","languageMap":{"en-US":"Project Space Alert Strategy Creation, Project ID: {0}, Policy Name: {1}","zh-HK":"項目空間告警策略創建，項目id:{0}，策略名稱：{1}"}},{"groupName":"workspace-alert-policy","regex":"项目空间告警策略批量删除,项目id:{0}","languageMap":{"en-US":"Batch deletion of project space alarm strategy, project ID: {0}","zh-HK":"項目空間告警策略批量删除，項目id:{0}"}}]}]}]
