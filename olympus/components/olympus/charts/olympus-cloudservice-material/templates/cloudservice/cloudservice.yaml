apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: CloudService
metadata:
  name: unified-platform
spec:
  description: 云原生统一门户底座，提供组织管理、项目管理、云服务管理等基础服务
  displayName: 基本门户平台
  icon:
    description: unified platform
    name: unified-cloundserving-title
  name: unified-platform
  templates:
    - spec:
        clusterPolicy: managed
        description: 提供统一门户平台的基础功能
        displayName: 统一门户portal
        matchLabels:
          app: olympus-portal
        name: olympus-portal
        namespaces:
          - caas-system
        vendor: harmonycloud
        version: olympus-v1.1.1
    - spec:
        clusterPolicy: managed
        description: 提供统一门户平台的基础功能
        displayName: 统一门户controller-manager
        matchLabels:
          app: cloudservice-operator
        name: olympus-controller-manager
        namespaces:
          - caas-system
        vendor: harmonycloud
        version: cloudservice-operator-v1.1.1
    - spec:
        clusterPolicy: managed
        description: 数据库
        displayName: MySQL
        matchLabels:
          operatorname: mysql-operator
        name: mysql
        namespaces:
          - caas-system
        vendor: harmonycloud
        version: v8.0.26-hc.7
    - spec:
        clusterPolicy: managed
        description: redis
        displayName: redis
        matchLabels:
          app: api-redis-service
        name: redis
        namespaces:
          - caas-system
        vendor: harmonycloud
        version: v6.2.6
    - spec:
        clusterPolicy: managed
        description: 提供统一门户的核心API服务
        displayName: 核心API服务
        matchLabels:
          app: olympus-core
        name: olympus-core
        namespaces:
          - caas-system
        vendor: harmonycloud
        version: olympus-v1.1.1
    - spec:
        clusterPolicy: managed
        description: 用户认证体系
        displayName: 用户组件
        matchLabels:
          app: app-management-svc
        name: app-management-svc
        namespaces:
          - caas-system
        vendor: harmonycloud
        version: olympus-v1.1.1
    - spec:
        clusterPolicy: managed
        description: 门户UI组件
        displayName: 门户UI组件
        matchLabels:
          app: caas-ui
        name: caas-ui
        namespaces:
          - caas-system
        vendor: harmonycloud
        version: olympus-v1.1.1
    - spec:
        clusterPolicy: managed
        description: 平台告警组件
        displayName: 平台告警组件
        matchLabels:
          app: caas-oam
        name: caas-oam
        namespaces:
          - caas-system
        vendor: harmonycloud
        version: olympus-v1.1.1
    - spec:
        clusterPolicy: managed
        description: 提供平台Harbor等镜像服务支持
        displayName: 镜像服务组件
        matchLabels:
          app: caas-registry
        name: caas-registry
        namespaces:
          - caas-system
        vendor: harmonycloud
        version: olympus-v1.1.1
    - spec:
        clusterPolicy: managed
        description: 多集群管控组件
        displayName: 多集群管控组件
        matchLabels:
          app: stellaris-core
        name: stellaris-core
        namespaces:
          - stellaris-system
        vendor: harmonycloud
        version: 1.1.0-alpha1
    - spec:
        clusterPolicy: work
        description: 多集群代理proxy组件
        displayName: 多集群代理
        matchLabels:
          app: stellaris-proxy-proxy
        name: stellaris-proxy-proxy
        namespaces:
          - stellaris-system
        vendor: harmonycloud
        version: 1.1.0-alpha1
    - spec:
        clusterPolicy: work
        description: 监控
        displayName: 监控
        name: monitor
        workloadReferences:
          - name: metrics-server
            namespace: kube-system
            kind: Deployment
            apiVersion: apps/v1
          - name: kube-prometheus-stack-kube-state-metrics
            namespace: monitoring
            kind: Deployment
            apiVersion: apps/v1
          - name: kube-prometheus-stack-operator
            namespace: monitoring
            kind: Deployment
            apiVersion: apps/v1
          - name: kube-prometheus-stack-operator
            namespace: monitoring
            kind: Deployment
            apiVersion: apps/v1
          - name: kube-prometheus-stack-prometheus-node-exporter
            namespace: monitoring
            kind: DaemonSet
            apiVersion: apps/v1
          - name: alertmanager-kube-prometheus-stack-alertmanager
            namespace: monitoring
            kind: StatefulSet
            apiVersion: apps/v1
          - name: prometheus-kube-prometheus-stack-prometheus
            namespace: monitoring
            kind: StatefulSet
            apiVersion: apps/v1
        vendor: harmonycloud
        version: v2.32.1
    - spec:
        clusterPolicy: work
        description: 日志
        displayName: 日志
        workloadReferences:
          - name: elasticsearch-exporter
            namespace: logging
            kind: Deployment
            apiVersion: apps/v1
          - name: elasticsearch-operator
            namespace: logging
            kind: Deployment
            apiVersion: apps/v1
          - name: log-pilot
            namespace: logging
            kind: DaemonSet
            apiVersion: apps/v1
          - name: elasticsearch-kibana
            namespace: logging
            kind: Deployment
            apiVersion: apps/v1
          - name: elasticsearch-master
            namespace: logging
            kind: StatefulSet
            apiVersion: apps/v1
          - name: logstash-logstash
            namespace: logging
            kind: StatefulSet
            apiVersion: apps/v1
        name: log
        vendor: harmonycloud
        version: 7.16.3
    - spec:
        clusterPolicy: work
        description: 资源池
        displayName: 资源池
        matchLabels:
          app: node-pool
        name: node-pool
        namespaces:
          - caas-system
        vendor: harmonycloud
        version: 0.1.1
    - spec:
        clusterPolicy: work
        description: 西西弗斯
        displayName: 西西弗斯
        name: sisyphus
        workloadReferences:
          - name: mongodb-deployment
            namespace: sisyphus-system
            kind: Deployment
            apiVersion: apps/v1
          - name: sisyphus
            namespace: sisyphus-system
            kind: Deployment
            apiVersion: apps/v1
        vendor: harmonycloud
        version: v1.2.0
    - spec:
        clusterPolicy: work
        description: 镜像仓库
        displayName: 镜像仓库
        matchLabels:
          app: harbor
        name: harbor
        namespaces:
          - harbor-system
        vendor: community
        version: v2.2.3-hc.2
    - spec:
        clusterPolicy: work
        description: 统一网络模型
        displayName: 统一网络模型
        name: heimdallr
        workloadReferences:
          - name: heimdallr-controller-manager
            namespace: heimdallr-system
            kind: Deployment
            apiVersion: apps/v1
          - name: heimdallr-node
            namespace: heimdallr-system
            kind: DaemonSet
            apiVersion: apps/v1
        vendor: harmonycloud
        version: 1.0.0-rc3
    - spec:
        clusterPolicy: work
        description: 域名解析
        displayName: 域名解析
        name: coredns
        workloadReferences:
          - name: coredns
            namespace: kube-system
            kind: Deployment
            apiVersion: apps/v1
        vendor: community
        version: v1.8.6
    - spec:
        clusterPolicy: work
        description: 网络隔离
        displayName: 网络隔离
        name: acl
        workloadReferences:
          - name: mystra-proxy
            namespace: mystra-system
            kind: Deployment
            apiVersion: apps/v1
          - name: mystra-agent
            namespace: mystra-system
            kind: DaemonSet
            apiVersion: apps/v1
        vendor: harmonycloud
        version: v1.1.0
    - spec:
        clusterPolicy: work
        description: 故障隔离
        displayName: 故障隔离
        name: node-isolation
        workloadReferences:
          - name: node-isolation-controller
            namespace: caas-system
            kind: Deployment
            apiVersion: apps/v1
          - name: problem-controller-deployment
            namespace: caas-system
            kind: Deployment
            apiVersion: apps/v1
          - name: node-isolation-agent
            namespace: caas-system
            kind: DaemonSet
            apiVersion: apps/v1
        vendor: harmonycloud
        version: v1.0.1-fix
  vendor: harmonycloud
  version: olympus-v1.1.1
