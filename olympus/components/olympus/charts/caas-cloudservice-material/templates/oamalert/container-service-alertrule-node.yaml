apiVersion: oam.unified-platform.harmonycloud.cn/v1alpha1
kind: AlertRule
metadata:
  labels:
    cloudService: container-service
    scope: platform
  name: alertrule-container-service-node
spec:
  objectGroups:
    - granularity:
        - alias: 集群
          all: false
          multiple: false
          name: cluster
          path: /cluster
          protocol: get
          responses:
            - cluster
          url: http://portal/getClusters
        - alias: 节点
          all: true
          multiple: true
          name: container-service-node
          path: /cluster/container-service-node
          protocol: get
          requires:
            - cluster
          responses:
            - container-service-node
          tips: 指选择集群下所有节点（包含后面新纳管的节点）
          url: http://olympus-core-svc:8080/portal/clusters/{cluster}/nodes
      objectType: container-service-node-alert
      objectTypeAlias: 节点告警
      objectTypePath: '{"name":"container-service","alias":"容器服务","children":[{"name":"container-service-node-alert","alias":"节点告警"}]}'
      promRules:
        - alias: 主机CPU使用占比
          expr: harmonycloud_node_cpu_rate{kubernetes_pod_node_name=~"{{`{{container-service-node}}`}}.*"}
          name: container-service-node_cpu_rate
          object: '{{`{{ $labels.kubernetes_pod_node_name }}`}}'
          reason: '{{`{{cluster}}`}}集群{{`{{ $labels.kubernetes_pod_node_name }}`}}节点的cpu使用率'
          type: metric
          unit: '%'
        - alias: 主机磁盘占比
          expr: harmonycloud_node_filesystem_using{kubernetes_pod_node_name=~"{{`{{container-service-node}}`}}.*"}
            / harmonycloud_node_filesystem_total{kubernetes_pod_node_name=~"{{`{{container-service-node}}`}}.*"}
          name: container-service-node_disk_rate
          object: '{{`{{ $labels.kubernetes_pod_node_name }}`}}'
          reason: '{{`{{cluster}}`}}集群的{{`{{ $labels.kubernetes_pod_node_name }}`}}节点的磁盘使用率'
          type: metric
          unit: '%'
        - alias: 主机内存使用占比
          expr: harmonycloud_node_memory_rate{kubernetes_pod_node_name=~"{{`{{container-service-node}}`}}.*"}
          name: container-service-node_memory_rate
          object: '{{`{{ $labels.kubernetes_pod_node_name }}`}}'
          reason: '{{`{{cluster}}`}}集群{{`{{ $labels.kubernetes_pod_node_name }}`}}节点的内存使用率'
          type: metric
          unit: '%'
        - alias: 主机不可用
          expr: (sum by (node)(kube_node_status_condition{node=~"{{`{{container-service-node}}`}}.*",condition="Ready"
            ,status=~"false|unknown"})>0) or (sum by (node)(kube_node_status_condition{node=~"{{`{{container-service-node}}`}}.*",condition="NetworkUnavailable"
            ,status=~"true|unknown"})>0)
          name: container-service-node_unvailable
          object: '{{`{{ $labels.node }}`}}'
          reason: '{{`{{cluster}}`}}集群的{{`{{ $labels.node }}`}}节点不可用'
          type: event
      replaceParams:
        - cluster
        - container-service-node
