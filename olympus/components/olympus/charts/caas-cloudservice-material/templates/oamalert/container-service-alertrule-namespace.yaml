apiVersion: oam.unified-platform.harmonycloud.cn/v1alpha1
kind: AlertRule
metadata:
  labels:
    cloudService: container-service
    scope: project
  name: alertrule-unified-platform-namespace
spec:
  objectGroups:
  - granularity:
    - alias: 集群
      all: false
      multiple: false
      name: cluster
      path: /cluster
      protocol: get
      responses:
      - cluster
      url: http://portal/getClusters
    - alias: 命名空间
      all: false
      multiple: true
      name: container-service-namespace
      path: /cluster/container-service-namespace
      protocol: get
      requires:
      - cluster
      responses:
      - container-service-namespace
      url: http://olympus-core-svc:8080/portal/clusters/{cluster}/project/{projectId}/getNamespaces
    objectType: container-service-namespace-alert
    objectTypeAlias: 命名空间告警
    objectTypePath: '{"name":"container-service","alias":"容器服务","children":[{"name":"container-service-namespace-alert","alias":"命名空间告警"}]}'
    promRules:
    - alias: 命名空间内存使用量
      expr: round(sum by( namespace) (container_memory_working_set_bytes{container!~"|POD",endpoint!="",namespace=~"{{`{{container-service-namespace}}`}}"})/1024/1024,0.01)
      name: container-service-namespace_memory_usage
      object: '{{`{{ $labels.namespace }}`}}'
      reason: '{{`{{cluster}}`}}集群的命名空间{{`{{ $labels.namespace }}`}}内存使用量'
      type: metric
      unit: Mb
    - alias: 命名空间内存使用占比
      expr: round((sum by (namespace)(harmonycloud_container_memory_usage_seconds_total{namespace=~"{{`{{container-service-namespace}}`}}"}and(harmonycloud_container_spec_memory_limit_bytes{namespace=~"{{`{{container-service-namespace}}`}}"})!=0))/(sum
        by(namespace)(harmonycloud_container_spec_memory_limit_bytes{namespace=~"{{`{{container-service-namespace}}`}}"})!=0)*100,0.1)
      name: container-service-namespace_memory_rate
      object: '{{`{{ $labels.namespace }}`}}'
      reason: '{{`{{cluster}}`}}集群的命名空间{{`{{ $labels.namespace }}`}}内存使用率'
      type: metric
      unit: '%'
    - alias: 命名空间CPU使用量
      expr: round(sum(rate(container_cpu_usage_seconds_total{container!~"|POD",endpoint!="",namespace=~"{{`{{container-service-namespace}}`}}"}[3m]))
        by (namespace)*1000,0.01)
      name: container-service-namespace_cpu_usage
      object: '{{`{{ $labels.namespace }}`}}'
      reason: '{{`{{cluster}}`}}集群的命名空间{{`{{ $labels.namespace }}`}}CPU使用量'
      type: metric
      unit: core
    - alias: 命名空间CPU使用占比
      expr: round((sum by(namespace)(harmonycloud_container_cpu_usage_seconds_total{namespace=~"{{`{{container-service-namespace}}`}}"}
        and (harmonycloud_container_spec_cpu_quota{namespace=~"{{`{{container-service-namespace}}`}}"}!=0))/(sum
        by(namespace)(harmonycloud_container_spec_cpu_quota{namespace=~"{{`{{container-service-namespace}}`}}"}!=0)))*100,0.1)
      name: container-service-namespace_cpu_rate
      object: '{{`{{ $labels.namespace }}`}}'
      reason: '{{`{{cluster}}`}}集群的命名空间{{`{{ $labels.namespace }}`}}CPU使用率'
      type: metric
      unit: '%'
    replaceParams:
    - cluster
    - container-service-namespace
