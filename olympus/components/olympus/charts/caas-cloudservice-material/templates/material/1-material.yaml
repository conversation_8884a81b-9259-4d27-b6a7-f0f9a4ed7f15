apiVersion: cloud-service.harmonycloud.cn/v1alpha1
kind: Material
metadata:
  name: olympus-container-service-1
  annotations:
    message.material.harmonycloud.cn/reapply: "true"
spec:
  cloudServiceName: container-service
  waitPolicy:
    onSuccess:
      - name: olympus-container-service-0
  tasks:
    # 菜单 - 容器服务
    - name: container-service
      kind: permissionImport
      permissionConfig:
        app:
          code: container_manager
        permissions: |-
          [{"name":"总览","code":"container_service_sys_container_service_overview","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_Overview","url":"/project/space/overview","type":"menu","method":"get","sort":1,"annotation":{"resources":["apps/deployments","apps/statefulsets","apps/daemonsets","apps/replicasets","batch/jobs","batch/cronjobs","pods","services","secrets","configmaps","persistentvolumeclaims","persistentvolumes","storage.k8s.io/storageclasses","serviceaccounts","namespaces","nodes","resourcequotas","harmonycloud.cn/nodepools","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/networkresources"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}}]},{"name":"应用市场","code":"container_service_sys_application_market","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_MarketApp","url":"/applicationMarket","type":"menu","method":"get","sort":2,"annotation":{"ceiling":true,"titleDescription":"Helm模板及应用模板中心"},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"新增分类","code":"skyview_application_market_type_add","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/appstore/types/{appType}/tags","type":"permission","method":"post"},{"name":"编辑分类","code":"skyview_application_market_type_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/appstore/types/{appType}/tags","type":"permission","method":"put"},{"name":"删除分类","code":"skyview_application_market_type_delete","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/appstore/types/{appType}/tags","type":"permission","method":"delete"},{"name":"新增","code":"skyview_application_market_helm_chart_create","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/appstore/charts/{chartName}/versions","type":"permission","method":"post"},{"name":"chart管理","code":"skyview_application_market_helm_chart_manager","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"调整分类","code":"skyview_application_market_helm_chart_type_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/appstore/types/{appType}/tags","type":"permission","method":"get"},{"name":"下架","code":"skyview_application_market_helm_chart_delete","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete"},{"name":"上传新版本","code":"skyview_application_market_helm_chart_version_create","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"立即推送","code":"skyview_application_market_helm_chart_version_notifications_push","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"}]},{"name":"应用管理","code":"container_service_sys_project_applications","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_ApplicationManage","url":"","type":"menu","method":"get","sort":3,"children":[{"name":"单集群应用","code":"container_service_sys_project_applicaiton_single","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/application/single","type":"menu","method":"get","annotation":{"resources":["core.oam.dev/applications","apps/deployments","apps/statefulsets","apps/daemonsets","apps/replicasets","batch/jobs","batch/cronjobs","pods","services","secrets","configmaps","persistentvolumeclaims","persistentvolumes","storage.k8s.io/storageclasses","serviceaccounts","namespaces","resourcequotas","nodes","harmonycloud.cn/nodepools","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/networkresources","endpoints","harmonycloud.cn/oamapprevisions","stellaris.harmonycloud.cn/aggregatedresources","expose.helper.harmonycloud.cn/layer4exposes"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"删除版本","code":"version_delete","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list","delete"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"新增","code":"skyview_application_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"保存草稿","code":"skyview_application_template_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"重新加载","code":"skyview_application_reload","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/organizations/{organiz/clusters/{clusterName}/projects/{projectId}/allowGpuRemoteCall","type":"permission","method":"post"},{"name":"重新启动","code":"skyview_application_restart","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"启动组件","code":"skyview_application_component_start","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"批量启动","code":"skyview_application_batch_start","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"停止组件","code":"skyview_application_component_stop","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"批量停止","code":"skyview_application_batch_stop","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"组件实例数变更","code":"skyview_application_component_edit_replicas","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"版本编辑更新","code":"skyview_application_version_update","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"执行批次","code":"skyview_application_execute","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"完成升级","code":"skyview_application_update_finish","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"接管流量","code":"skyview_application_version_flow","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"回滚升级","code":"skyview_application_update_rollback","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"版本回滚","code":"skyview_application_version_rollback","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"保存拓扑图","code":"skyview_application_edit_canvas","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"hpa新增","code":"skyview_application_hpa_add","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace}/apps/{appName}/component/{componentName}/hpas","type":"permission","method":"post","annotation":{"component":"hpa","componentName":"水平扩缩容","resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"hpa编辑","code":"skyview_application_hpa_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace}/apps/{appName}/component/{componentName}/hpas","type":"permission","method":"put","annotation":{"component":"hpa","componentName":"水平扩缩容","resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"hpa删除","code":"skyview_application_hpa_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace}/apps/{appName}/hpas","type":"permission","method":"delete","annotation":{"component":"hpa","componentName":"水平扩缩容","resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"集群内部暴露","code":"skyview_application_expose_internalService","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"集群外部暴露","code":"skyview_application_expose_externalServices","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"删除","code":"skyview_application_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch","delete"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"草稿继续编辑","code":"skyview_application_draft_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"草稿删除","code":"skyview_application_draft_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"纳管","code":"skyview_application_decompile","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"component":"app-decompile","componentName":"应用反编译"}},{"name":"单集群关联资源","code":"skyview_application_associations","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace}/apps/{appName}/associations,/clusters/{clusterName}/resource/aggregate","type":"permission","method":"get","annotation":{"component":"resource-aggregate","componentName":"资源关联控制器"}},{"name":"hpa查询","code":"skyview_application_hpa_list","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace}/apps/{appName}/hpas","type":"permission","method":"get","annotation":{"component":"hpa","componentName":"水平扩缩容"}},{"name":"应用监控","code":"skyview_application_monitor","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"component":"monitoring","componentName":"监控"}},{"name":"文件上传","code":"skyview_application_file_upload","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"文件下载","code":"skyview_application_file_download","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"文件路径查询","code":"skyview_application_file_pwd","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch"],"endpoints":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}}]},{"name":"多集群应用","code":"container_service_sys_project_applicaiton_multi","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/application/multi","type":"menu","method":"get","annotation":{"resources":["core.oam.dev/applications","apps/deployments","apps/statefulsets","apps/daemonsets","apps/replicasets","batch/jobs","batch/cronjobs","pods","services","secrets","configmaps","persistentvolumeclaims","persistentvolumes","storage.k8s.io/storageclasses","serviceaccounts","namespaces","resourcequotas","stellaris.harmonycloud.cn/aggregatedresources","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings","expose.helper.harmonycloud.cn/layer4exposes","nodes","harmonycloud.cn/nodepools","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/networkresources","endpoints","harmonycloud.cn/oamapprevisions","stellaris.harmonycloud.cn/clustertopologies","stellaris.harmonycloud.cn/servicesyncs","stellaris.harmonycloud.cn/clusters"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"新增","code":"skyview_multi_application_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"编辑","code":"edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"保存草稿","code":"skyview_multi_application_template_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"批量启动","code":"skyview_multi_application_batch_start","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"批量停止","code":"skyview_multi_application_batch_stop","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"编辑调度策略","code":"skyview_multi_scheduling_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"草稿继续编辑","code":"skyview_multi_application_draft_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"草稿删除","code":"skyview_multi_application_draft_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"集群内部暴露新增","code":"skyview_multi_application_expose_internalService","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"集群外部暴露新增","code":"skyview_multi_application_expose_externalServices","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"删除","code":"skyview_multi_application_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create","update","patch","delete"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list","update"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list","update"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"保存拓扑图","code":"skyview_multi_application_edit_canvas","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"多集群hpa查询","code":"skyview_multi_application_cluster_hpa_query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"多集群hpa新增","code":"skyview_multi_application_cluster_hpa_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"多集群hpa编辑","code":"skyview_multi_application_cluster_hpa_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"put","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"多集群hpa删除","code":"skyview_multi_application_cluster_hpa_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"编辑自治集群","code":"skyview_multi_application_autonomy_cluster_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"put","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"文件上传","code":"skyview_multi_application_file_upload","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"文件下载","code":"skyview_multi_application_file_download","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"文件路径查询","code":"skyview_multi_application_file_pwd","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","update","patch"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"删除集群","code":"skyview_multi_application_remove_cluster","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"core.oam.dev/applications":["get","list","create"],"endpoints":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"harmonycloud.cn/oamapprevisions":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"serviceaccounts":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/aggregatedresources":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"多集群服务聚合","code":"skyview_multi_application_service_sync","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"core.oam.dev/applications":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/servicesyncs":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"多集群服务聚合新增","code":"skyview_multi_application_service_sync_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"core.oam.dev/applications":["get","list","update","patch"],"services":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/servicesyncs":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"多集群服务聚合编辑","code":"skyview_multi_application_service_sync_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"put","annotation":{"resource_option":{"core.oam.dev/applications":["get","list","update","patch"],"services":["get","list"],"stellaris.harmonycloud.cn/clustertopologies":["get","list"],"stellaris.harmonycloud.cn/servicesyncs":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"多集群服务聚合删除","code":"skyview_multi_application_service_sync_delete","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete","annotation":{"resource_option":{"core.oam.dev/applications":["get","list","update","patch"],"services":["get","list"],"stellaris.harmonycloud.cn/servicesyncs":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"Helm Chart服务","code":"container_service_sys_project_helm_chart_service","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/application/helmChart","type":"menu","method":"get","children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"put"},{"name":"应用市场新增","code":"skyview_helm_chart_to_application_market_add","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/appstore/types/chart/tags","type":"permission","method":"post"},{"name":"升级","code":"skyview_helm_chart_upgrade","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/upgrade","type":"permission","method":"put"},{"name":"版本回滚","code":"skyview_helm_chart_version_rollback","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/rollback","type":"permission","method":"put"},{"name":"参数对比","code":"skyview_helm_chart_yaml_compare","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/revision/{revision}/values","type":"permission","method":"get"},{"name":"文件下载","code":"skyview_helm_chart_download","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/revision/{revision}/download","type":"permission","method":"get"},{"name":"服务暴露编辑","code":"skyview_helm_chart_expose_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"put"},{"name":"删除","code":"skyview_helm_chart_delete","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}","type":"permission","method":"delete"}]}]},{"name":"工作负载","code":"container_service_sys_project_workload","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_Workload","url":"","type":"menu","method":"get","sort":4,"children":[{"name":"无状态部署","code":"container_service_sys_project_deployment","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/resource/deployment/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","apps/deployments","apps/replicasets","events","namespaces","pods","resourcequotas"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/deployments,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment},/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/describe,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/events,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/metadata,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/pods,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets/{replicaset}/yaml,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/yaml","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"新增","code":"skyview_deployment_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","create","update"],"apps/replicasets":["get","list","create","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑副本数","code":"skyview_replicas_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","create","update"],"apps/replicasets":["get","list","create","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑元数据","code":"skyview_deployment_metadata_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","create","update"],"apps/replicasets":["get","list","create","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"版本管理","code":"skyview_deployment_version","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","create","update"],"apps/replicasets":["get","list","create","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"查看版本yaml","code":"skyview_deployment_yaml_check","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"yaml对比","code":"skyview_deployment_yaml_compare","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"版本回滚","code":"skyview_deployment_version_rollback","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/revisions/{revision}/rollback","type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","update"],"apps/replicasets":["get","list","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑yaml","code":"skyview_deployment_yaml_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/deployments":["get","list","update"],"apps/replicasets":["get","list","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"skyview_deployment_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}","type":"permission","method":"get","annotation":{"component":"node-up-down","resource_option":{"apps/deployments":["get","list","delete"],"apps/replicasets":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"有状态部署","code":"container_service_sys_project_statefulset","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/resource/statefulset/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","events","resourcequotas","apps/statefulsets","pods"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/statefulsets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"新增","code":"skyview_statefulset_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/statefulsets":["get","list","create","update"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑副本数","code":"skyview_replicas_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/statefulsets":["get","list","create","update","patch"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑元数据","code":"skyview_statefulset_metadata_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/statefulsets":["get","list","create","update","patch"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑yaml","code":"skyview_statefulset_yaml_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/statefulsets":["get","list","create","update","patch"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"skyview_statefulset_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/statefulsets":["get","list","delete"],"namespaces":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"守护进程","code":"container_service_sys_project_daemonset","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/resource/daemonset/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","apps/daemonsets","pods"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"新增","code":"skyview_daemonset_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list","create","update"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑元数据","code":"skyview_daemonset_metadata_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑yaml","code":"skyview_daemonset_yaml_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"skyview_daemonset_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"普通任务","code":"container_service_sys_project_job","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/resource/job/list","type":"menu","method":"get","annotation":{"resources":["pods","resourcequotas","stellaris.harmonycloud.cn/clusters","batch/jobs","events","namespaces"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"新增","code":"skyview_job_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/jobs":["get","list","create"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"重新执行","code":"skyview_job_restart","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/jobs":["get","list","create","update","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑元数据","code":"skyview_job_metadata_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/jobs":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑yaml","code":"skyview_job_yaml_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/jobs":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"skyview_job_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/jobs":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"定时任务","code":"container_service_sys_project_cronjob","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/resource/cronjob/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","batch/cronjobs","pods"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/cronjobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"新增","code":"add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/cronjobs":["get","list","create"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"启停","code":"schedule","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/cronjobs":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑元数据","code":"editMetaData","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/cronjobs":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑策略","code":"editInfo","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/cronjobs":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑yaml","code":"editYaml","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/cronjobs":["get","list","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除执行记录","code":"deleteJob","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/cronjobs":["get","list","create","update","patch","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"delete","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"batch/cronjobs":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"Pod容器组","code":"container_service_sys_project_pod_container_group","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/resource/pod","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","harmonycloud.cn/nodepools","events","pods","nodes"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"文件查询","code":"pod_file_pwd","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"pods":["get","list"]}}},{"name":"文件下载","code":"pod_file_download","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"pods":["get","list"]}}},{"name":"监控","code":"monitor","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"pod日志","code":"containerLog","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"pod控制台","code":"containerTerminal","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"事件","code":"event","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"查看yaml","code":"yaml","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"harmonycloud.cn/nodepools":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list","delete"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]}]},{"name":"配置挂载","code":"container_service_sys_project_config_mount","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_ConfigMounting","url":"","type":"menu","method":"get","sort":5,"children":[{"name":"配置文件","code":"container_service_sys_project_configMap","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/configMap/list","type":"menu","method":"get","annotation":{"resources":["apps/statefulsets","namespaces","apps/deployments","apps/daemonsets","resourcequotas","stellaris.harmonycloud.cn/clusters","configmaps","apps/replicasets","stellaris.harmonycloud.cn/multiclusterresources","batch/jobs","stellaris.harmonycloud.cn/multiclusterresourcebindings","pods","batch/cronjobs","events"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create","update","patch","delete"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create","update","patch","delete"]}}},{"name":"新增","code":"skyview_configmap_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list","create"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create"]}}},{"name":"编辑挂载数据","code":"skyview_configmap_data_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create","update","patch"]}}},{"name":"编辑元数据","code":"skyview_configmap_meta_data_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑Yaml","code":"skyview_configmap_yaml_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list","create","update","patch"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"skyview_configmap_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"configmaps":["get","list","delete"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","delete"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","delete"]}}},{"name":"多集群配置文件查询","code":"skyview_multi_cluster_configmap_list","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"多集群配置文件新增","code":"skyview_multi_cluster_add_configmap","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"多集群配置文件编辑","code":"skyview_multi_cluster_edit_configmap","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"put"},{"name":"多集群配置文件删除","code":"skyview_multi_cluster_delete_configmap","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete"},{"name":"多集群配置文件查看差异化配置","code":"skyview_multi_cluster_compare_configmap","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"}]},{"name":"保密字典","code":"container_service_sys_project_Secret","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/secret/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","events","pods","apps/daemonsets","apps/deployments","stellaris.harmonycloud.cn/multiclusterresources","stellaris.harmonycloud.cn/multiclusterresourcebindings","namespaces","resourcequotas","apps/statefulsets","secrets","batch/cronjobs","apps/replicasets","batch/jobs"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create","update","patch","delete"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create","update","patch","delete"]}}},{"name":"新增","code":"skyview_secret_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create"]}}},{"name":"编辑加密数据","code":"skyview_secret_data_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create","update","patch"]}}},{"name":"编辑元数据","code":"skyview_secret_meta_data_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑Yaml","code":"skyview_secret_yaml_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"skyview_secret_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"batch/cronjobs":["get","list"],"batch/jobs":["get","list"],"events":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","delete"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","delete"]}}},{"name":"多集群保密字典查询","code":"skyview_multi_cluster_secret_list","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"多集群保密字典新增","code":"skyview_multi_cluster_add_secret","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"多集群保密字典编辑","code":"skyview_multi_cluster_edit_secret","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"put"},{"name":"多集群保密字典删除","code":"skyview_multi_cluster_delete_secret","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete"},{"name":"多集群保密字典查看差异化配置","code":"skyview_multi_cluster_compare_secret","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"}]}]},{"name":"存储","code":"container_service_sys_storage_service","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_Storage","url":"","type":"menu","method":"get","sort":6,"children":[{"name":"存储卷声明","code":"container_service_sys_project_PVC","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/pvc/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/multiclusterresources","storage.k8s.io/storageclasses","pods","resourcequotas","persistentvolumeclaims","stellaris.harmonycloud.cn/multiclusterresourcebindings","persistentvolumes","stellaris.harmonycloud.cn/clusters","events","namespaces"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create","update","patch","delete"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create","update","patch","delete"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"新增","code":"skyview_pvc_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","create"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"扩容","code":"skyview_pvc_expand","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","update","patch"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create","update","patch"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"编辑元数据","code":"skyview_pvc_meta_data_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","update","patch"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create","update","patch"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"编辑Yaml","code":"skyview_pvc_yaml_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","update","patch"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","create","update","patch"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","create","update","patch"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"删除","code":"skyview_pvc_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list","delete"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"stellaris.harmonycloud.cn/multiclusterresourcebindings":["get","list","delete"],"stellaris.harmonycloud.cn/multiclusterresources":["get","list","delete"],"storage.k8s.io/storageclasses":["get","list"]}}},{"name":"多集群存储卷声明查询","code":"skyview_multi_cluster_pvc_list","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"多集群存储卷声明新增","code":"skyview_multi_cluster_add_pvc","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"多集群存储卷声明编辑","code":"skyview_multi_cluster_edit_pvc","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"put"},{"name":"多集群存储卷声明删除","code":"skyview_multi_cluster_delete_pvc","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete"},{"name":"多集群存储卷查看差异化配置","code":"skyview_multi_cluster_compare_pvc","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"}]},{"name":"存储卷","code":"container_service_sys_project_PV","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/pv/list","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","pods","storage.k8s.io/storageclasses","persistentvolumeclaims","persistentvolumes"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"events":["get","list"],"namespaces":["get","list"],"persistentvolumeclaims":["get","list"],"persistentvolumes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"],"storage.k8s.io/storageclasses":["get","list"]}}}]}]},{"name":"网络","code":"container_service_sys_project_network_service","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_Network","url":"","type":"menu","method":"get","sort":7,"children":[{"name":"Service服务","code":"container_service_sys_project_service","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/network/service","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","services","expose.helper.harmonycloud.cn/layer4exposes","networking.k8s.io/ingresses","expose.helper.harmonycloud.cn/ingressclasses","configmaps","endpoints","apps/deployments","apps/statefulsets","apps/daemonsets","apps/replicasets","pods","nodes"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list"],"endpoints":["get","list"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"新增","code":"skyview_service_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","create","update","patch"],"endpoints":["get","list","create"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","create"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑","code":"skyview_service_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"新增四层对外路由","code":"skyview_service_add_four_layer_exponse","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"新增七层对外路由","code":"skyview_service_add_seven_layer_exponse","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑对外路由","code":"edit_domains","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除对外路由","code":"remove_domains","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch","delete"],"endpoints":["get","list","update","patch","delete"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","delete"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch","delete"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑元数据","code":"skyview_service_metadata_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑yaml","code":"skyview_service_yaml_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch"],"endpoints":["get","list","update","patch"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","create"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"skyview_service_delete","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"delete","annotation":{"resource_option":{"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list","update","patch","delete"],"endpoints":["get","list","update","patch","delete"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list","update","patch"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list","delete"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list","update","patch","delete"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"services":["get","list","update","patch","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"Ingress路由","code":"container_service_sys_project_ingress","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/network/ingress","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","services","expose.helper.harmonycloud.cn/layer4exposes","networking.k8s.io/ingresses","expose.helper.harmonycloud.cn/ingressclasses","apisix.apache.org/apisixroutes","configmaps","secrets","endpoints","apps/deployments","apps/statefulsets","apps/daemonsets","apps/replicasets","pods","nodes"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"apisix.apache.org/apisixroutes":["get","list"],"apps/daemonsets":["get","list"],"apps/deployments":["get","list"],"apps/replicasets":["get","list"],"apps/statefulsets":["get","list"],"configmaps":["get","list"],"endpoints":["get","list"],"events":["get","list"],"expose.helper.harmonycloud.cn/ingressclasses":["get","list"],"expose.helper.harmonycloud.cn/layer4exposes":["get","list"],"namespaces":["get","list"],"networking.k8s.io/ingresses":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"services":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list","watch"]}}},{"name":"新增Nginx","code":"add_nginx","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{}},{"name":"编辑Nginx","code":"edit_nginx","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{}},{"name":"删除Nginx","code":"delete_nginx","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{}},{"name":"新增APISIX","code":"add_apisix","kind":"resource","resource":{"code":"project","appCode":"application"},"method":"get","type":"permission","annotation":{}},{"name":"编辑APISIX","code":"edit_apisix","kind":"resource","resource":{"code":"project","appCode":"application"},"method":"get","type":"permission","annotation":{}},{"name":"删除APISIX","code":"delete_apisix","kind":"resource","method":"get","resource":{"code":"project","appCode":"application"},"type":"permission","annotation":{}},{"name":"新增TLS","code":"add_tls","kind":"resource","method":"get","resource":{"code":"project","appCode":"application"},"type":"permission","annotation":{}},{"name":"编辑TLS","code":"edit_tls","kind":"resource","resource":{"code":"project","appCode":"application"},"method":"get","type":"permission","annotation":{}},{"name":"删除TLS","code":"delete_tls","kind":"resource","resource":{"code":"project","appCode":"application"},"method":"get","type":"permission","annotation":{}},{"name":"新增会话保持","code":"add_session","kind":"resource","resource":{"code":"project","appCode":"application"},"method":"get","type":"permission","annotation":{}},{"name":"编辑会话保持","code":"edit_session","kind":"resource","resource":{"code":"project","appCode":"application"},"method":"get","type":"permission","annotation":{}},{"name":"删除会话保持","code":"delete_session","kind":"resource","resource":{"code":"project","appCode":"application"},"method":"get","type":"permission","annotation":{}}]}]},{"name":"Kubernetes资源","code":"container_service_kubernetes_crd_resource","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"ip-pool-menu","url":"/project/space/kubernetesres","type":"menu","method":"get","sort":8,"annotation":{},"children":[{"name":"查询","code":"kubernetes_resource_query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{}},{"name":"编辑yaml","code":"kubernetes_edit_yaml","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{}},{"name":"删除","code":"kubernetes_resource_delete","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{}}]},{"name":"虚拟机","code":"container_service_sys_workspace_vm","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_VirtualMachine","url":"/project/space/virtualMachines","type":"menu","method":"get","sort":8,"annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","kubevirt.io/virtualmachines","kubevirt.io/virtualmachineinstances","cdi.kubevirt.io/datavolumes","kubeovn.io/ips","kubeovn.io/subnets","secrets","configmaps","pods","nodes","events","resourcequotas"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"cdi.kubevirt.io/datavolumes":["get","list"],"configmaps":["get","list"],"events":["get","list"],"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list"],"kubevirt.io/virtualmachineinstances":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"创建","code":"vm_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"cdi.kubevirt.io/datavolumes":["get","list","create","delete"],"configmaps":["get","list","create"],"events":["get","list"],"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list"],"kubevirt.io/virtualmachineinstances":["get","list"],"kubevirt.io/virtualmachines":["get","list","create"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","create"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑","code":"vm_edit","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"cdi.kubevirt.io/datavolumes":["get","list","create"],"configmaps":["get","list"],"events":["get","list"],"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list"],"kubevirt.io/virtualmachineinstances":["get","list"],"kubevirt.io/virtualmachines":["get","list","update"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"vm_delete","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"cdi.kubevirt.io/datavolumes":["get","list","delete"],"configmaps":["get","list","delete"],"events":["get","list"],"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list"],"kubevirt.io/virtualmachineinstances":["get","list"],"kubevirt.io/virtualmachines":["get","list","delete"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list","delete"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"启动","code":"vm_start","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"cdi.kubevirt.io/datavolumes":["get","list"],"configmaps":["get","list"],"events":["get","list"],"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list"],"kubevirt.io/virtualmachineinstances":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"停止","code":"vm_stop","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"cdi.kubevirt.io/datavolumes":["get","list"],"configmaps":["get","list"],"events":["get","list"],"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list"],"kubevirt.io/virtualmachineinstances":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"重启","code":"vm_restart","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"cdi.kubevirt.io/datavolumes":["get","list"],"configmaps":["get","list"],"events":["get","list"],"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list"],"kubevirt.io/virtualmachineinstances":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"VNC控制台","code":"vm_vnc","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post","annotation":{"resource_option":{"cdi.kubevirt.io/datavolumes":["get","list"],"configmaps":["get","list"],"events":["get","list"],"kubeovn.io/ips":["get","list"],"kubeovn.io/subnets":["get","list"],"kubevirt.io/virtualmachineinstances":["get","list"],"kubevirt.io/virtualmachines":["get","list"],"namespaces":["get","list"],"nodes":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"secrets":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]},{"name":"仓库及模板","code":"container_service_sys_repository_template","kind":"resource","resource":{"code":"project","appCode":"application"},"icon":"v35_RepositoryTemplate","url":"","type":"menu","method":"get","sort":9,"children":[{"name":"镜像仓库","code":"container_service_sys_image_repository","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/repository","type":"menu","method":"get","children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"上传","code":"upload_image","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"编辑仓库标签","code":"repo_label_manage","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"查看版本","code":"view_version","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"删除镜像","code":"remove_image","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"添加规则","code":"add_rule","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"立即执行","code":"execute_rule","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"模拟运行","code":"simulation_run","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"编辑规则","code":"edit_rule","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"启用/禁用规则","code":"enable_disable_rule","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"删除规则","code":"remove_rule","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"编辑定时任务","code":"edit_timed_task","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"中止","code":"suspend_rule","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"日志","code":"log","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"内容信任","code":"content_trust","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"阻止潜在漏洞镜像","code":"Block_potential_vulnerability_mirroring","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"自动扫描镜像","code":"auto_scan","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"编辑CVE白名单","code":"CVE_whitelist","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"post"},{"name":"镜像复制","code":"copy","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"构建记录","code":"build_record","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"漏洞扫描","code":"bug_scan","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"拉取/下载","code":"download","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"标签管理","code":"label_manage","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"删除镜像版本","code":"images_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"},{"name":"部署镜像","code":"deploy","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get"}]},{"name":"网络模板","code":"container_service_sys_project_space_network_template","kind":"resource","resource":{"code":"project","appCode":"application"},"url":"/project/space/network/template","type":"menu","method":"get","annotation":{"resources":["stellaris.harmonycloud.cn/clusters","namespaces","resourcequotas","events","heimdallr.harmonycloud.cn/networkresources","heimdallr.harmonycloud.cn/networkdetails","heimdallr.harmonycloud.cn/hdareas","heimdallr.harmonycloud.cn/hdblocks","heimdallr.harmonycloud.cn/hdpods","heimdallr.harmonycloud.cn/hdpools","heimdallr.harmonycloud.cn/hdsvcs","isolate.harmonycloud.cn/hleases","mystra.heimdallr.harmonycloud.cn/podpolicies","configmaps","pods"]},"children":[{"name":"查询","code":"query","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list"],"heimdallr.harmonycloud.cn/hdblocks":["get","list"],"heimdallr.harmonycloud.cn/hdpods":["get","list"],"heimdallr.harmonycloud.cn/hdpools":["get","list"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list"],"heimdallr.harmonycloud.cn/networkdetails":["get","list"],"heimdallr.harmonycloud.cn/networkresources":["get","list"],"isolate.harmonycloud.cn/hleases":["get","list"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"新增","code":"skyview_project_space_network_template_add","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch"],"heimdallr.harmonycloud.cn/networkresources":["get","list","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"编辑","code":"skyview_project_space_network_template_update","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","create","update","patch","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","create","update","patch","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete","create","update","patch"],"isolate.harmonycloud.cn/hleases":["get","list","create","update","patch","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","create","update","patch","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}},{"name":"删除","code":"skyview_project_space_network_template_remove","kind":"resource","resource":{"code":"project","appCode":"application"},"type":"permission","method":"get","annotation":{"resource_option":{"configmaps":["get","list","delete"],"events":["get","list"],"heimdallr.harmonycloud.cn/hdareas":["get","list","delete"],"heimdallr.harmonycloud.cn/hdblocks":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpods":["get","list","delete"],"heimdallr.harmonycloud.cn/hdpools":["get","list","delete"],"heimdallr.harmonycloud.cn/hdsvcs":["get","list","delete"],"heimdallr.harmonycloud.cn/networkdetails":["get","list","delete"],"heimdallr.harmonycloud.cn/networkresources":["get","list","delete"],"isolate.harmonycloud.cn/hleases":["get","list","delete"],"mystra.heimdallr.harmonycloud.cn/podpolicies":["get","list","delete"],"namespaces":["get","list"],"pods":["get","list"],"resourcequotas":["get","list"],"stellaris.harmonycloud.cn/clusters":["get","list"]}}}]}]}]