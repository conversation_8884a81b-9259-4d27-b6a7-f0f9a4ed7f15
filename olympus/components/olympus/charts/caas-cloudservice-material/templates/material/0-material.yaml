apiVersion: cloud-service.harmonycloud.cn/v1alpha1
kind: Material
metadata:
  name: olympus-container-service-0
  annotations:
    message.material.harmonycloud.cn/reapply: "true"
spec:
  cloudServiceName: container-service
  tasks:
    # 前台应用 - 容器服务
    - name: fount-application
      kind: appImport
      appConfigs:
        - code: container_manager
          name: 容器服务
          url: /container
          frameControl: true
          type: front
          workspaceType: projectSwitch
          icon: v35_ContainerService
          sortID: 700