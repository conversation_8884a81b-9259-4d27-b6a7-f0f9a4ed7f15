kind: Deployment
apiVersion: apps/v1
metadata:
  name: {{ .Values.name }}
  labels:
    app: {{ .Values.name }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.name }}
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 70
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - {{ .Values.name }}
                topologyKey: kubernetes.io/hostname
      volumes:
        - name: devops-app-4
          configMap:
            name: {{ .Values.name }}
            items:
              - key: efficiency.conf
                path: efficiency.conf
            defaultMode: 420
            optional: false
        - name: devops-app-5
          configMap:
            name: {{ .Values.name }}
            items:
              - key: main.conf
                path: main.conf
            defaultMode: 420
            optional: false
        - name: devops-app-6
          configMap:
            name: {{ .Values.name }}
            items:
              - key: nginx.conf
                path: nginx.conf
            defaultMode: 420
            optional: false
      containers:
        - name: {{ .Values.name }}
          {{- if  .Values.image.devopsAppTag|contains "replace_env" }}
          image: "{{ tpl .Values.image.repository . }}/{{ tpl .Values.image.name . }}:{{ tpl .Values.image.tag . }}"
          {{- else }}
          image: "{{ tpl .Values.image.repository . }}/{{ tpl .Values.image.name . }}:{{ default .Values.image.devopsAppTag }}"
          {{- end }}
          ports:
            - name: tcp-8000
              containerPort: 8000
              protocol: TCP
            - name: tcp-8001
              containerPort: 8001
              protocol: TCP
            - name: tcp-8100
              containerPort: 8100
              protocol: TCP
            - name: tcp-8101
              containerPort: 8101
              protocol: TCP
            - name: tcp-8102
              containerPort: 8102
              protocol: TCP
            - name: tcp-8103
              containerPort: 8103
              protocol: TCP
            - name: tcp-8104
              containerPort: 8104
              protocol: TCP
            - name: tcp-8105
              containerPort: 8105
              protocol: TCP
            - name: tcp-8106
              containerPort: 8106
              protocol: TCP
            - name: tcp-8107
              containerPort: 8107
              protocol: TCP
            - name: tcp-8108
              containerPort: 8108
              protocol: TCP
            - name: tcp-8109
              containerPort: 8109
              protocol: TCP
          env:
            - name: TZ
              value: Asia/Shanghai
          resources:
{{- include "app.resource.templete" . | indent 12 }}
          volumeMounts:
            - name: devops-app-4
              mountPath: /opt/bitnami/nginx/conf/server_blocks/efficiency.conf
              subPath: efficiency.conf
            - name: devops-app-5
              mountPath: /opt/bitnami/nginx/conf/bitnami/main.conf
              subPath: main.conf
            - name: devops-app-6
              mountPath: /opt/bitnami/nginx/conf/nginx.conf
              subPath: nginx.conf
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 5
            httpGet:
              path: /
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 180
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          readinessProbe:
            failureThreshold: 5
            httpGet:
              path: /
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 20
            successThreshold: 1
            timeoutSeconds: 5
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      automountServiceAccountToken: true
      securityContext: {}
      schedulerName: default-scheduler