apiVersion: v1
data:
  node-reset-config.yaml: |-
    solutionInfos:
      - baselineVersion: {{.Values.version}}
        solutionName: reset-cluster-nodes
        solutionVersion: {{.Values.nodeResetVersion}}
    group:
      reset: 待重置节点
    nodeResetStepGroups:
      - code: node-reset-group
        alias: 节点重置
        steps:
          - code: sisyphus-system-step-prepare-execution
            alias: 执行部署平台初始化步骤
            labels:
              - nodeResetLabel
          - code: reset-nodes
            alias: 重置节点
            labels:
              - nodeResetLabel
          - code: uninstall-containerd
            alias: 卸载容器运行时并清除用户数据
            labels:
              - nodeResetLabel
          - code: uninstall-sisyphus-flag
            alias: 卸载sisyphus初始化步骤标记
            labels:
              - nodeResetLabel
    initial:
      nodeResetSisyphusSolutionApplyCode: sisyphus-solution-apply-node-reset
      nodeResetSisyphusSolutionApplyAlias: 节点重置信息提交
kind: ConfigMap
metadata:
  name: node-reset-config
