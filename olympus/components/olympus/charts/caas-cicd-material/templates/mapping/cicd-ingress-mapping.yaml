apiVersion: cloud-service.harmonycloud.cn/v1alpha1
kind: IngressMapping
metadata:
  name: devops
spec:
  cloudServiceName: container-service
  properties:
    ingress:
      annotations:
        nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
        nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
        nginx.ingress.kubernetes.io/ssl-redirect: "false"
  rules:
    - backend:
        ingress:
          paths:
            - backend:
                service:
                  name: devops-app
                  port:
                    number: 8000
              path: /efficiency/
              pathType: Prefix
        type: Ingress
      name: front