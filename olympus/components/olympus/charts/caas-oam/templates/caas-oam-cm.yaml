kind: ConfigMap
apiVersion: v1
metadata:
  name: caas-oam-cm
data:
  oam.properties: |-
    springfox.documentation.swagger-ui.enabled: false
    amp.operation.component=unified-platform
    spring.datasource.url=jdbc:mysql://{{ .Values.global.mysql.serviceName }}:{{ .Values.global.mysql.port }}/oam?useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai
    spring.datasource.username=${mysql_username}
    spring.datasource.password=${mysql_password}
    spring.datasource.driver-class-name=com.mysql.jdbc.Driver
    spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
    spring.resources.static-locations=file:/usr/local/oam-files/pdf/
    mybatis.mapperLocations[0]= classpath*:mapper/*.xml
    mybatis.typeAliasesPackage=cn.harmonycloud.k8s.oam.dal.po
    gateway.url=http://caas-gateway-svc:8081
    k8s.mars.api.username=system
    k8s.mars.api.password=QWIxMjM0NTY=
    prometheus.url=http://prometheusapi-service:8099
    server.port=8080
    spring.redis.host={{ .Values.global.redis.serviceName }}
    spring.redis.port=6379
    spring.redis.password=${redis_password}
    spring.redis.timeout=5000
    api.access.allow.origin=*
    k8s.mars.address.host=http://{{ .Values.global.f5Address }}
    k8s.mars.address.cluster=${k8s.mars.address.host}/#/cluster/clusterDetail
    k8s.mars.address.component=${k8s.mars.address.host}/#/overview/overviewNormal/
    k8s.mars.address.service=${k8s.mars.address.host}/#/manage/serviceDetail/
    k8s.mars.address.stateService=${k8s.mars.address.host}/#/manage/statefulSetDetail/
    pdf.font.flag=false
    pdf.font.path=msyh.ttf
    pdf.path=/usr/local/oam-files/pdf/
    grafana.port=30115
    grafana.dashboards.path=/dashboards
    spring.main.allow-bean-definition-overriding=true
    olympuscore.url=http://olympus-core-svc:8080
    caas.url=http://caas-api-svc:8080
    caascore.url=http://caas-core-svc:8080
    middlewareClusterIdMap={}
    scheduling.enabled=true
    amp.url=http://devops-amp-svc.{{ .Release.Namespace }}:8080
    devops.amp.url=http://devops-amp-svc:8080
    logging.config=/logback.xml
    disaster.recovery={{ .Values.global.disaster.enable }}