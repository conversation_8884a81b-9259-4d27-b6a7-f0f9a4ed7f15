{{- if .Values.ha -}}
apiVersion: cna.harmonycloud.cn/v1
kind: JenkinsCluster
metadata:
  name: {{ .Values.name }}
spec:
  username: admin
  password: Ab123456
  replicas: {{ .Values.replicaCount }}
  rsync:
    args:
      - --exclude=.*
      - --exclude=workspace
    container:
      image: "{{ tpl .Values.image.repository . }}/{{ tpl .Values.rsync.image.name . }}"
      imagePullPolicy: {{ .Values.imagePullPolicy }}
      name: rsync
      resources:
        {{- toYaml .Values.rsync.resources | nindent 6 }}
      volumeMounts:
        - mountPath: /data
          name: jenkins-data
  container:
    env:
      - name: LIMITS_MEMORY
        valueFrom:
          resourceFieldRef:
            divisor: 1Mi
            resource: limits.memory
      - name: JAVA_OPTS
        value: -Xmx3072m -XshowSettings:vm -Dhudson.security.csrf.GlobalCrumbIssuerConfiguration.DISABLE_CSRF_PROTECTION=true
          -Dhudson.slaves.NodeProvisioner.initialDelay=0 -<PERSON>hu<PERSON>.slaves.NodeProvisioner.MARGIN=50 -Dhudson.model.DirectoryBrowserSupport.CSP=
          -Dhudson.slaves.NodeProvisioner.MARGIN0=0.85 -Duser.timezone=Asia/Shanghai -Dpermissive-script-security.enabled=true
      - name: JENKINS_OPTS
        value: -Difile.encoding=utf-8
    image: "{{ tpl .Values.image.repository . }}/{{ tpl .Values.image.name . }}"
    imagePullPolicy: {{ .Values.imagePullPolicy }}
    name: jenkins
    ports:
      - containerPort: 8080
        name: web
        protocol: TCP
      - containerPort: 50000
        name: agent
        protocol: TCP
    readinessProbe:
      failureThreshold: 10
      httpGet:
        path: /login
        port: 8080
        scheme: HTTP
      initialDelaySeconds: 60
      periodSeconds: 10
      successThreshold: 1
      timeoutSeconds: 5
    resources:
      {{- toYaml .Values.resources | nindent 6 }}
    volumeMounts:
      - mountPath: /var/jenkins_home
        name: jenkins-data
  initContainer:
    command:
      - /bin/sh
      - -c
      - if [ ! -f '/var/jenkins_home/init' ]; then cp -rf /jenkins-init/* /var/jenkins_home/;fi;sysctl -w fs.inotify.max_user_watches=8192000;chown -R root:root /var/jenkins_home
    image: "{{ tpl .Values.image.repository . }}/{{ tpl .Values.image.name . }}"
    imagePullPolicy: IfNotPresent
    name: init
    securityContext:
      privileged: true
      runAsUser: 0
    resources:
      {{- toYaml .Values.resources | nindent 6 }}
    volumeMounts:
      - mountPath: /var/jenkins_home
        name: jenkins-data
  volumeClaimTemplates:
  - metadata:
      name: jenkins-data
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: {{ .Values.storageSize }}
      storageClassName: {{ .Values.storageClass }}
  {{- with .Values.tolerations }}
  tolerations:
  {{ toYaml . | indent 0 }}
  {{- end }}
  {{- with .Values.nodeSelector }}
  nodeSelector:
  {{ toYaml . | indent 2 }}
  {{- end }}
  {{- if .Values.imagePullSecrets }}
  imagePullSecrets:
  {{ toYaml .Values.imagePullSecrets | indent 0 }}
  {{- end }}
  {{- if or (eq .Values.antiAffinity "hard") (eq .Values.antiAffinity "soft") }}
  affinity:
  {{- end }}
  {{- if eq .Values.antiAffinity "hard" }}
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
          - key: app
            operator: In
            values:
            - {{ .Values.name }}
        topologyKey: "kubernetes.io/hostname"
  {{- else if eq .Values.antiAffinity "soft" }}
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        podAffinityTerm:
          topologyKey: "kubernetes.io/hostname"
          labelSelector:
            matchExpressions:
            - key: app
              operator: In
              values:
              - {{ .Values.name }}
  {{- end }}
{{- end -}}