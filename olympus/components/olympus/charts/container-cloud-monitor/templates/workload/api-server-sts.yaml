apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: api-server
spec:
  serviceName: api-server-svc
  replicas: {{.Values.api_server_sts.replicas}}
  selector:
    matchLabels:
      name: api-server
  template:
    metadata:
      labels:
        name: api-server
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 70
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: name
                      operator: In
                      values:
                        - api-server
                topologyKey: kubernetes.io/hostname
      containers:
        - name: api-server
          image: {{.Values.global.image.repository}}/ebpf-apm-api-server:{{.Values.global.image.api_server}}
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 10
            httpGet:
              path: /actuator
              port: 9191
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 10
          readinessProbe:
            failureThreshold: 10
            httpGet:
              path: /actuator
              port: 9191
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 10
          resources:
{{- toYaml .Values.api_server_sts.resources | nindent 12 }}
          ports:
            - name: http
              containerPort: 9191
          volumeMounts:
            - mountPath: /etc/localtime
              name: time
            - name: config
              mountPath: /root/config/application.properties
              subPath: application.properties
            - name: config
              mountPath: /root/config/log4j2.xml
              subPath: log4j2.xml
            - mountPath: /root/bin/start.sh
              name: config
              subPath: start.sh
            - mountPath: /root/config/es.properties
              name: config
              subPath: es.properties
      volumes:
        - hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai
            type: ""
          name: time
        - name: config
          configMap:
            defaultMode: 511
            name: api-server-config
