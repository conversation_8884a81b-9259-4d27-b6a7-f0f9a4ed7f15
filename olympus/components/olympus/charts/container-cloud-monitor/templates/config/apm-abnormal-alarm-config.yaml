apiVersion: v1
data:
  application.properties: |-
    apm.alarm.kafka.consumer.enabled=false
    server.port=9191
    ### application name ###
    unique.application.name=apm-alarm
    #################### database configuration #####################
    ############ for upgrade mysql versiopn to 8.0.X  ############
    spring.datasource.url=************************************************************************************************************************************************************************************************
    #database.password=wpjyEymJI0lm5nXj4DgzDxvGv9xaFHGMqteQY2h/x6s=
    spring.datasource.username=Uds2bW/IeHO1bcw+730dsQ==
    spring.datasource.password=UpSZzslFFkNgQEnloislXg==
    spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
    #####hikari连接池设置
    spring.datasource.type=org.vlis.apm.alarm.config.MybatisDataSource
    spring.datasource.hikari.minimum-idle=20
    spring.datasource.hikari.maximum-pool-size=200
    spring.datasource.hikari.auto-commit=true
    spring.datasource.hikari.idle-timeout=20000
    spring.datasource.hikari.pool-name=AlarmHikariCP
    spring.datasource.hikari.max-lifetime=20000
    spring.datasource.hikari.connection-timeout=30000
    spring.datasource.hikari.connection-test-query=SELECT 1
    mybatis-plus.mapper-locations=classpath:mapper/*.xml
    mybatis-plus.configuration.map-underscore-to-camel-case=true
    mybatis-plus.configuration.cache-enabled=false
    mybatis-plus.global-config.refresh=true
    mybatis-plus.global-config.db-config.id-type=auto
    mybatis-plus.global-config.db-config.field-strategy=not_empty
    mybatis-plus.global-config.db-config.db-column-underline=true
    mybatis-plus.global-config.db-config.logic-delete-value=1
    mybatis-plus.global-config.db-config.logic-not-delete-value=0
    mybatis-plus.global-config.db-config.db-type=mysql
    ##是否需要打印mybatis日志
    mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
    # kafka 配置
    apm.data.receiver.kafka.authentication.scram.switch=false
    apm.data.receiver.kafka.authentication.scram.mechanism=SCRAM-SHA-256
    apm.data.receiver.kafka.authentication.scram.config=org.apache.kafka.common.security.scram.ScramLoginModule required username=\"admin\" password=\"adminpwd\";
    spring.kafka.bootstrap-servers=kafka-svc:59092
    spring.kafka.consumer.enable-auto-commit=true
    spring.kafka.consumer.max-poll-records=2000
    spring.kafka.consumer.auto-offset-reset=earliest
    spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
    spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

    kafka.topic.alarm-input=alarm-input,alarm-input-4tuple
    apm.data.receiver.kafka.topic = PAAS_ALARM_INPUT
    apm.data.receiver.kafka.groupId = GID_PAAS_EBPF_ALARM
    logging.config=classpath:log4j2.xml
    # logging.level.com.harmonycloud.bsm.alert=debug
    # 告警保留天数
    apm.alarm.preserve.days=30
    ##线程池核心线程数量
    alarm.thread.core=6
    ##是否开启prometheus beat的相关告警
    prometheus.beat.enable=false
    ## 事件检测线程池配置参数,coresize = cpu核数*2
    alarm.metric.worker.pool.coresize=4
    alarm.metric.worker.pool.maxsize=8
    alarm.metric.worker.pool.queuesize=5000
    ## 告警处理线程池配置参数
    alarm.alarm.worker.pool.coresize=100
    alarm.alarm.worker.pool.maxsize=150
    alarm.alarm.worker.pool.queuesize=1000

    ###### ===========  heartbeat  ==========######
    application.monitor.heartbeat.enabled=false
    application.monitor.heartbeat.interval=300
    application.monitor.center.url=http://apm-configserver-svc:8888/application-monitor/heartbeat

    ## 日志配置
    log4j2. =org.apache.logging.log4j.core.async.AsyncLoggerContextSelector
  configDaemon.properties: |-
    processName=org.vlis.apm.alarm.ApmAlarmApplication
    cpuLimit=99999
    memLimit=874
    startCmd=sh /root/bin/start.sh
    stopCmd=sh /root/bin/shutdown.sh
  es.properties: |-
    ###### elastic search cluster configuration  ######
    apm.elastic.search.cluster.name=docker-cluster
    apm.elastic.search.hosts=[apm-elasticsearch-master:9200]
    apm.elastic.search.index.prefix=apm2.0-
    # Elasticsearch集群是否开启了安全功能，如果开启则需要配置以下内容
    apm.elastic.search.security.enabled=true
    # 认证用户名和密码
    apm.elastic.search.security.auth=elastic:UpSZzslFFkNgQEnloislXg==
    # 证书和CA路径
    apm.elastic.search.ssl.key=/home/<USER>/instance.key
    apm.elastic.search.ssl.certificate=/home/<USER>/instance.crt
    apm.elastic.search.ssl.certificate.authorities=/home/<USER>/ca.crt

    # es多集群查询开关
    apm.elastic.ccs.switch=false
    # json格式配置es多集群
    apm.elastic.ccs.info=[{"clusterName":"docker-cluster","ipPorts":["************:9300"],"prefix":"apm2.0-","securityEnabled":true,"securityAuth":"elastic:hcapm123456","keyPath":"/home/<USER>/instance.key","crtPath":"/home/<USER>/instance.crt","crtAuthPath":"/home/<USER>/ca.crt","dataCenterName":"hzdc"},{"clusterName":"docker-cluster","ipPorts":["**************:9300"],"prefix":"apm2.0-","securityEnabled":true,"securityAuth":"elastic:hcapm123456","keyPath":"/home/<USER>/instance.key","crtPath":"/home/<USER>/instance.crt","crtAuthPath":"/home/<USER>/ca.crt","dataCenterName":"sjzx"}]
  log4j2.xml: |-
    <?xml version="1.0" encoding="UTF-8"?>
    <configuration>

      <Properties>
        <Property name="local.logging.path">../logs</Property>
        <Property name="logging.console.level">INFO</Property>
        <Property name="logging.file.path">${local.logging.path}/${project.name}</Property>
        <Property name="action.file.path">${logging.file.path}</Property>
        <Property name="project.name">apm-alarm</Property>
        <Property name="logging.file.name">${sys:logging.file.path}/info/${project.name}</Property>
        <Property name="logging.file.error.name">${sys:logging.file.path}/error/${project.name}-error</Property>
        <Property name="layout">%d %p %X{unique} [%t] %c{10}:%M:%L %m%n</Property>
      </Properties>

      <appenders>
        <console name="Console" target="SYSTEM_OUT">
          <PatternLayout>
            <pattern>${layout}</pattern>
          </PatternLayout>
          <!--<ThreadFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>-->
        </console>

        <RollingFile name="RollingFileInfo" fileName="${logging.file.name}.log"
          filePattern="${logging.file.name}-%d{yyyy-MM-dd-HH}.log">
          <PatternLayout pattern="${layout}"/>
          <!--<ThresholdFilter level="info"/>-->
          <Policies>
            <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
          </Policies>
          <DefaultRolloverStrategy>
            <Delete basePath="${logging.file.path}/info" maxDepth="2">
              <IfFileName glob="*.log"/>
              <IfLastModified age="7d"/>
            </Delete>
          </DefaultRolloverStrategy>
        </RollingFile>

        <RollingFile name="RollingFileError" fileName="${logging.file.error.name}.log"
          filePattern="${logging.file.error.name}-%d{yyyy-MM-dd-HH}.log">
          <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
          <PatternLayout pattern="${layout}"/>
          <Policies>
            <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
          </Policies>
          <DefaultRolloverStrategy>
            <Delete basePath="${logging.file.path}/error" maxDepth="1" testMode="true">
              <IfFileName glob="*.log"/>
              <!--保留7天-->
              <IfLastModified age="7d"/>
            </Delete>
          </DefaultRolloverStrategy>
        </RollingFile>
      </appenders>

      <Loggers>
        <Root level="INFO">
          <!--<AppenderRef ref="Console"/>-->
          <AppenderRef ref="RollingFileInfo"/>
          <AppenderRef ref="RollingFileError"/>
          <!--<AsyncLogger name="org" level = "info" includeLocation="false" additivity="false">-->
            <!--<AppenderRef ref="RollingFileInfo"/>-->
            <!--<AppenderRef ref="RollingFileError"/>-->
          <!--</AsyncLogger>-->
        </Root>
        <!--<Logger name="com.harmonycloud.bsm" level="debug" additivity="false">-->
          <!--<AppenderRef ref="RollingFileInfo"/>-->
        <!--</Logger>-->

      </Loggers>
    </configuration>
  start.sh: |-
    #!/bin/sh

        cd `dirname $0`
        echo `basename $0` is at `pwd`

        export APM_ALARM_HOME=..
        CONFIG_DIR=$APM_ALARM_HOME/config
        GCLOG=$APM_ALARM_HOME/logs

        RESOLVED_CONFIG_DIR=`cd "$CONFIG_DIR"; pwd`
        export CLASSPATH=$RESOLVED_CONFIG_DIR

        for i in `ls $APM_ALARM_HOME/lib/*.jar`; do
            CLASSPATH=$CLASSPATH:$i
        done
        exec java $JAVA_OPTS  -classpath $CLASSPATH -Xms1800m -Xmx2048m -Dfile.encoding=UTF-8 org.vlis.apm.alarm.ApmAlarmApplication $*
kind: ConfigMap
metadata:
  name: apm-abnormal-alarm-config