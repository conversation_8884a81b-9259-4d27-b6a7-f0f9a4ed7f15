#Parameters: {
  docking: {
    // +key=底座基线大版本
    // +desc=底座基线大版本
    version: *"3.0.3" | string
    // +key=A051.A001-节点上下线解决方案版本号
    // +desc=节点上下线解决方案版本号 随底座发版中进行说明
    nodeUpDownVersion: *"1.2.0-1.0.2-universal" | string
    // +key=A051.A002-节点重置解决方案版本号
    // +desc=创建集群解决方案版本号 随底座发版中进行说明
    nodeResetVersion: *"1.2.0-1.0.1-universal" | string
    // +key=A051.A003-创建集群解决方案版本号
    // +desc=创建集群解决方案版本号 随底座发版中进行说明
    clusterCreateVersion: *"3.0.4-1.5.0-universal" | string
  }
}
