#Parameters: {
  product: {
    // +key=管理集群主stellaris地址
    // +desc=用于集群纳管proxy上报
    stellarisMasterAddress: string
    // +key=管理集群主stellaris端口
    // +desc=默认值30530,用于集群纳管proxy上报
    stellarisMasterPort: *"29020" | string
    // +key=存储服务
    // +desc=平台后端服务pvc使用存储服务类型
    storageClassName: *"default" | string
    // +key=grafana面板部署namespace
    // +desc=grafana面板部署namespace 一般为monitoring，但如果为rancher集群则需要和客户确认
    grafanaDashboardsNamespace: *"monitoring" | string
  }
}
