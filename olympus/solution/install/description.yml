version: "v2"
kind: "Solution"
metadata:
  name: "olympus-install-solution"
  nickname: "统一门户v1.2.0安装解决方案"
  appVersion: "olympus-v1.2.0"
  version: "olympus-v1.2.0"
  labels:
    - "统一门户v1.2.0"
  description: "统一门户v1.2.0安装解决方案"
spec:
  groupClaims:
    - name: 主控节点
      includes:
        - "初始化主控节点"
  cueFiles:
    optionsFilePath: "cue/options.cue"
    globalVarFilePath: "cue/global_vars.cue"
    paramFiles:
      - name: "平台全局配置"
        path: "cue/parameters/global.cue"
      - name: "产品部署配置"
        path: "cue/parameters/product.cue"
      - name: "底座对接物料"
        path: "cue/parameters/docking.cue"
      - name: "主备环境配置"
        path: "cue/parameters/disaster.cue"
        when: context.options.disaster
      - name: "grafana-proxy配置"
        path: "cue/parameters/grafana-proxy.cue"
        when: context.options.clusterHasGrafana
      - name: "分区节点池配置"
        path: "cue/parameters/namespace-nodepool.cue"
        when: context.options.apmNotInDefaultNodePool || (context.options.containerWithCICD && context.options.containerCICDNotInDefaultNodePool)
  workflowFiles:
    - name: "数据库数据导入(MYSQL)"
      path: "workflows/databasedata/mysql/install.yaml"
      when: context.options.databaseDriver=="mysql"

    - name: "容器服务CICD-数据库数据导入(MYSQL)"
      path: "workflows/databasedata/mysql/install-cicd.yaml"
      when: context.options.databaseDriver=="mysql" && context.options.containerWithCICD

    - name: "公共配置数据导入"
      path: "workflows/product/sync/install.yaml"

    - name: "生产模式高可用-拉起统一门户核心服务"
      path: "workflows/product/olympus/install-produce-ha.yaml"
      when: context.options.deployMode=="生产模式"
    - name: "生产模式高可用-拉起容器服务核心服务"
      path: "workflows/product/caas/install-produce-ha.yaml"
      when: context.options.deployMode=="生产模式"


    - name: "生产模式非高可用-拉起统一门户核心服务"
      path: "workflows/product/olympus/install-produce-none-ha.yaml"
      when: context.options.deployMode=="生产模式非高可用"
    - name: "生产模式非高可用-拉起容器服务核心服务"
      path: "workflows/product/caas/install-produce-none-ha.yaml"
      when: context.options.deployMode=="生产模式非高可用"

    - name:  "创建CICD组件所在分区"
      path: "workflows/product/caas/create-devops-namespace.yaml"
      when: context.options.containerWithCICD

    - name:  "CICD组件所在分区节点池配置"
      path: "workflows/product/caas/patch-devops-namespace.yaml"
      when: context.options.containerWithCICD && context.options.containerCICDNotInDefaultNodePool



    - name: "容器服务升级CICD能力"
      path: "workflows/product/caas/install-produce-cicd.yaml"
      when: context.options.containerWithCICD

    - name:  "创建apm组件所在分区"
      path: "workflows/product/apm/create-cloudmonitor-namespace.yaml"

    - name:  "apm组件所在分区节点池配置"
      path: "workflows/product/apm/patch-cloudmonitor-namespace.yaml"
      when: context.options.apmNotInDefaultNodePool

    - name:  "生产模式高可用-拉起监控服务"
      path: "workflows/product/apm/install-apm-ha.yaml"
      when: context.options.deployMode=="生产模式"

    - name:  "生产模式非高可用-拉起监控服务"
      path: "workflows/product/apm/install-apm-none-ha.yaml"
      when: context.options.deployMode=="生产模式非高可用"


    - name: "权限数据导入"
      path: "workflows/product/permission/install.yaml"

    - name: "额外步骤 - 统一门户核心步骤升级主备能力"
      path: "workflows/product/olympus/disaster.yaml"
      when: context.options.disaster
    - name: "额外步骤 - 容器服务核心步骤升级主备能力"
      path: "workflows/product/caas/disaster.yaml"
      when: context.options.disaster

    - name: "额外步骤 - 开启caas-ui grafana-proxy能力"
      path: "workflows/product/olympus/grafana-proxy.yaml"
      when: context.options.clusterHasGrafana

    # 演示组织权限同步
    - name: "演示组织权限同步"
      path: "workflows/product/sync/build-in-organ-permission-sync.yaml"
