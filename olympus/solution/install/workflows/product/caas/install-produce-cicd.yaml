steps:
  - name: "caas-cicd-install"
    nickname: "容器服务升级CICD能力"
    type: component
    hosts: "初始化主控节点"
    target:
      name: "olympus-product"
      version: "1.2.0"
      actions:
        - "install-caas-cicd"
    output: |
      "caas-cicd-install": {
              image: {
                 repository: parameters.global.image.repository
              }
              storageClassName: "caas-lvm"
      }