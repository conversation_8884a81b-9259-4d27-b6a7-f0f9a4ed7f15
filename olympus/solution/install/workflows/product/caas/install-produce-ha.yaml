steps:
  - name: "caas-produce-ha-product-install"
    nickname: "生产模式非高可用-拉起容器服务核心服务"
    type: component
    hosts: "初始化主控节点"
    target:
      name: "olympus-product"
      version: "1.2.0"
      actions:
        - "install-caas"
    output: |
      "caas-produce-ha-product-install": {
              image: {
                 repository: parameters.global.image.repository
              }
              ingressIP: parameters.global.ingress.ingressIP
              storageClassName: parameters.product.storageClassName
              caasCoreSize: "2Gi"
              caas_core: {
                  replicas: 2
                  requests: {
                      cpu: "1",
                      memory: "2Gi",
                  }
                  limits: {
                      cpu: "1",
                      memory: "2Gi",
                  }
              }
      
      }

