steps:
  - name: "olympus-produce-ha-product-install"
    nickname: "生产模式非高可用-拉起统一门户核心服务"
    type: component
    hosts: "初始化主控节点"
    target:
      name: "olympus-product"
      version: "1.2.0"
      actions:
        - "install-olympus"
    output: |
      "olympus-produce-ha-product-install": {
              grafana: {
                dashboards: {
                  namespace: parameters.product.grafanaDashboardsNamespace
                }
              }
              image: {
                 repository: parameters.global.image.repository
              }
              docking: {
                 version: parameters.docking.version
                 nodeUpDownVersion: parameters.docking.nodeUpDownVersion
                 nodeResetVersion: parameters.docking.nodeResetVersion
                 clusterCreateVersion: parameters.docking.clusterCreateVersion
              }
              ingressIP: parameters.global.ingress.ingressIP
              storageClassName: parameters.product.storageClassName
              oamStorageSize: "10Gi"
              olympusCoreSize: "2Gi"
              stellarisMasterAddress: parameters.product.stellarisMasterAddress
              stellarisMasterPort: parameters.product.stellarisMasterPort
              oam_api: {
                  replicas: 2
                  requests: {
                      cpu: "1",
                      memory: "2Gi",
                  }
                  limits: {
                      cpu: "1",
                      memory: "2Gi",
                  }
              }
              caas_registry: {
                  replicas: 2
                  requests: {
                      cpu: "1",
                      memory: "2Gi",
                  }
                  limits: {
                      cpu: "1",
                      memory: "2Gi",
                  }
              }
              caas_ui: {
                  replicas: 2
                  requests: {
                      cpu: "1",
                      memory: "2Gi",
                  }
                  limits: {
                      cpu: "1",
                      memory: "2Gi",
                  }
              }
              cloudservice_operator: {
                  replicas: 1
                  requests: {
                      cpu: "500m",
                      memory: "512Mi",
                  }
                  limits: {
                      cpu: "1",
                      memory: "2Gi",
                  }
              }

              olympus_core: {
                  replicas: 2
                  requests: {
                      cpu: "1",
                      memory: "2Gi",
                  }
                  limits: {
                      cpu: "2",
                      memory: "4Gi",
                  }
              }
              olympus_portal: {
                  replicas: 2
                  requests: {
                      cpu: "1",
                      memory: "2Gi",
                  }
                  limits: {
                      cpu: "1",
                      memory: "2Gi",
                  }
              }
      
      }
