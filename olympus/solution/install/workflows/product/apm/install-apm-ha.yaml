steps:
  - name: "apm-produce-ha-product-install"
    nickname: "生产模式高可用-监控服务"
    type: component
    hosts: "初始化主控节点"
    target:
      name: "olympus-product"
      version: "1.2.0"
      actions:
        - "install-apm"
    output: |
      "apm-produce-ha-product-install": {
        ingressIP: parameters.global.ingress.ingressIP
        image: {
          repository: parameters.global.image.repository
        }
        abnormal_alarm_sts: {
          replicas: 2
          resources: {
            requests: {
              cpu: "2",
              memory: "2Gi"
            }
            limits: {
              cpu: "2",
              memory: "2Gi"
            }
          }
        }
        api_server_sts: {
          replicas: 2
          resources: {
            requests: {
              cpu: "2",
              memory: "4Gi"
            }
            limits: {
              cpu: "2",
              memory: "4Gi"
            }
          }
        }
        apm_configserver_sts: {
          replicas: 2
          resources: {
            requests: {
              cpu: "2",
              memory: "1400Mi"
            }
            limits: {
              cpu: "2",
              memory: "4Gi"
            }
          }
        }
        nephele_sts: {
          replicas: 2
          resources: {
            requests: {
              cpu: "1000m",
              memory: "1024Mi"
            }
            limits: {
              cpu: "1000m",
              memory: "1024Mi"
            }
          }
        }
      }
