steps:
  - name: "apm-produce-none-ha-product-install"
    nickname: "生产模式非高可用-监控服务"
    type: component
    hosts: "初始化主控节点"
    target:
      name: "olympus-product"
      version: "3.6.1"
      actions:
        - "install-apm"
    output: |
      "apm-produce-none-ha-product-install": {
        ingressIP: parameters.global.ingress.ingressIP
        image: {
          repository: parameters.global.image.repository
        }
        abnormal_alarm_sts: {
          replicas: 1
          resources: {
            requests: {
              cpu: "1",
              memory: "1Gi"
            }
            limits: {
              cpu: "2",
              memory: "2Gi"
            }
          }
        }
        api_server_sts: {
          replicas: 1
          resources: {
            requests: {
              cpu: "500m",
              memory: "1Gi"
            }
            limits: {
              cpu: "2",
              memory: "2Gi"
            }
          }
        }
        apm_configserver_sts: {
          replicas: 1
          resources: {
            requests: {
              cpu: "1",
              memory: "1Gi"
            }
            limits: {
              cpu: "2",
              memory: "2Gi"
            }
          }
        }
        nephele_sts: {
          replicas: 1
          resources: {
            requests: {
              cpu: "500m",
              memory: "512Mi"
            }
            limits: {
              cpu: "500m",
              memory: "512Mi"
            }
          }
        }
      }
