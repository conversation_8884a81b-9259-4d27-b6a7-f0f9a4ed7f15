version: "v2"
kind: "Solution"
metadata:
  name: "olympus-base-upgrade"
  nickname: "统一门户v4.2.1基座平台升级解决方案"
  appVersion: "olympus-v4.2.1"
  version: "olympus-v4.2.1"
  labels:
    - "统一门户v4.2.1"
  description: "统一门户v4.2.1基座平台升级解决方案"
spec:
  groupClaims:
    - name: 主控节点
      includes:
        - "初始化主控节点"
  cueFiles:
    optionsFilePath: "cue/options.cue"
    globalVarFilePath: "cue/global_vars.cue"
    paramFiles:
      - name: "平台全局配置"
        path: "cue/parameters/global.cue"
      - name: "平台对接负载均衡配置"
        path: "cue/parameters/ingress.cue"
  workflowFiles:
    - name: "备份数据库数据"
      path: "workflows/database/backup-mysql.yaml"
      when: context.options.databaseDriver=="mysql"
    - name: "升级数据库数据"
      path: "workflows/database/upgrade.yaml"
    - name: "升级基坐平台"
      path: "workflows/product/base/upgrade.yaml"